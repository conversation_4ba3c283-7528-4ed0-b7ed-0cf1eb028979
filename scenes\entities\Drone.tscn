[gd_scene load_steps=5 format=3 uid="uid://c1d2e3f4g5h6i"]

[ext_resource type="Script" path="res://scripts/entities/Drone.cs" id="1_drone"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 8.0

[sub_resource type="CircleShape2D" id="CircleShape2D_2"]
radius = 20.0

[sub_resource type="CircleShape2D" id="CircleShape2D_3"]
radius = 30.0

[node name="Drone" type="CharacterBody2D"]
collision_layer = 1
collision_mask = 0
script = ExtResource("1_drone")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.8, 1, 1)
scale = Vector2(16, 16)

[node name="SelectionArea" type="Area2D" parent="."]
collision_layer = 1
collision_mask = 0

[node name="SelectionCollision" type="CollisionShape2D" parent="SelectionArea"]
shape = SubResource("CircleShape2D_2")

[node name="CombatArea" type="Area2D" parent="."]
collision_layer = 1
collision_mask = 2

[node name="CombatCollision" type="CollisionShape2D" parent="CombatArea"]
shape = SubResource("CircleShape2D_3")

[node name="NavigationAgent2D" type="NavigationAgent2D" parent="."]
path_desired_distance = 4.0
target_desired_distance = 4.0

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

[node name="SelectionIndicator" type="Node2D" parent="."]
visible = false

[node name="SelectionRing" type="Line2D" parent="SelectionIndicator"]
points = PackedVector2Array(20, 0, 14.1421, 14.1421, 0, 20, -14.1421, 14.1421, -20, 0, -14.1421, -14.1421, 0, -20, 14.1421, -14.1421, 20, 0)
width = 2.0
default_color = Color(0, 1, 1, 1)

[node name="EnergyDrainTimer" type="Timer" parent="."]
wait_time = 1.0
autostart = true
