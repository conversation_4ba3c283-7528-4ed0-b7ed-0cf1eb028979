using Godot;
using System;
using System.Collections.Generic;

public partial class MainUI : CanvasLayer
{
    [Export] public PackedScene DroneScene { get; set; }

    // Resource UI elements
    private ProgressBar _fuelBar;
    private ProgressBar _lifeSupportBar;
    private ProgressBar _hullBar;
    private Label _fuelLabel;
    private Label _lifeSupportLabel;
    private Label _hullLabel;
    private Label _biomassLabel;

    // Info UI elements
    private Label _sectorLabel;
    private Label _droneLabel;
    private Label _selectedLabel;
    private Label _gameStateLabel;

    // Pattern buttons
    private Button _spiralButton;
    private Button _pincerButton;
    private Button _scatterButton;

    // System references
    private GameManager _gameManager;
    private ResourceManager _resourceManager;
    private DroneSwarmManager _droneSwarmManager;
    private SectorGenerator _sectorGenerator;

    // Patterns
    private Dictionary<string, Pattern> _availablePatterns = new();

    public override void _Ready()
    {
        GetUIReferences();
        ConnectToSystems();
        SetupPatterns();
        ConnectUISignals();
        
        GD.Print("MainUI initialized");
    }

    private void GetUIReferences()
    {
        // Resource bars
        _fuelBar = GetNode<ProgressBar>("HUD/ResourcePanel/ResourceContainer/FuelBar");
        _lifeSupportBar = GetNode<ProgressBar>("HUD/ResourcePanel/ResourceContainer/LifeSupportBar");
        _hullBar = GetNode<ProgressBar>("HUD/ResourcePanel/ResourceContainer/HullBar");
        
        // Resource labels
        _fuelLabel = GetNode<Label>("HUD/ResourcePanel/ResourceContainer/FuelBar/FuelLabel");
        _lifeSupportLabel = GetNode<Label>("HUD/ResourcePanel/ResourceContainer/LifeSupportBar/LifeSupportLabel");
        _hullLabel = GetNode<Label>("HUD/ResourcePanel/ResourceContainer/HullBar/HullLabel");
        _biomassLabel = GetNode<Label>("HUD/ResourcePanel/ResourceContainer/BiomassLabel");

        // Info labels
        _sectorLabel = GetNode<Label>("HUD/InfoPanel/InfoContainer/SectorLabel");
        _droneLabel = GetNode<Label>("HUD/InfoPanel/InfoContainer/DroneLabel");
        _selectedLabel = GetNode<Label>("HUD/InfoPanel/InfoContainer/SelectedLabel");
        _gameStateLabel = GetNode<Label>("HUD/InfoPanel/InfoContainer/GameStateLabel");

        // Pattern buttons
        _spiralButton = GetNode<Button>("HUD/PatternPanel/PatternContainer/SpiralButton");
        _pincerButton = GetNode<Button>("HUD/PatternPanel/PatternContainer/PincerButton");
        _scatterButton = GetNode<Button>("HUD/PatternPanel/PatternContainer/ScatterButton");
    }

    private void ConnectToSystems()
    {
        // Get system references
        _gameManager = GetNode<GameManager>("/root/GameManager");
        _resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        _droneSwarmManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        _sectorGenerator = GetNode<SectorGenerator>("/root/SectorGenerator");

        // Connect to system signals
        if (_gameManager != null)
        {
            _gameManager.GameStateChanged += OnGameStateChanged;
        }

        if (_resourceManager != null)
        {
            _resourceManager.ResourceChanged += OnResourceChanged;
        }

        if (_droneSwarmManager != null)
        {
            _droneSwarmManager.DronesSelected += OnDronesSelected;
        }

        if (_sectorGenerator != null)
        {
            _sectorGenerator.SectorGenerated += OnSectorGenerated;
        }
    }

    private void SetupPatterns()
    {
        // Create default patterns
        _availablePatterns["Spiral"] = Pattern.CreateSpiralPattern();
        _availablePatterns["Pincer"] = Pattern.CreatePincerPattern();
        _availablePatterns["Scatter"] = Pattern.CreateScatterPattern();
    }

    private void ConnectUISignals()
    {
        // Connect pattern buttons
        _spiralButton.Pressed += () => ExecutePattern("Spiral");
        _pincerButton.Pressed += () => ExecutePattern("Pincer");
        _scatterButton.Pressed += () => ExecutePattern("Scatter");
    }

    private void OnGameStateChanged(int newState)
    {
        var state = (GameManager.GameState)newState;
        _gameStateLabel.Text = $"State: {state}";
        
        // Update UI based on game state
        switch (state)
        {
            case GameManager.GameState.Hunting:
                // Disable some UI elements during hunting
                break;
            case GameManager.GameState.GameOver:
                ShowGameOverUI();
                break;
        }
    }

    private void OnResourceChanged(int resourceType, float currentAmount, float maxAmount)
    {
        var type = (ResourceManager.ResourceType)resourceType;
        
        switch (type)
        {
            case ResourceManager.ResourceType.Fuel:
                UpdateResourceBar(_fuelBar, _fuelLabel, "Fuel", currentAmount, maxAmount);
                break;
            case ResourceManager.ResourceType.LifeSupport:
                UpdateResourceBar(_lifeSupportBar, _lifeSupportLabel, "Life Support", currentAmount, maxAmount);
                break;
            case ResourceManager.ResourceType.Hull:
                UpdateResourceBar(_hullBar, _hullLabel, "Hull", currentAmount, maxAmount);
                break;
            case ResourceManager.ResourceType.Biomass:
                _biomassLabel.Text = $"Biomass: {currentAmount:F0}";
                break;
        }
    }

    private void UpdateResourceBar(ProgressBar bar, Label label, string resourceName, float current, float max)
    {
        bar.MaxValue = max;
        bar.Value = current;
        label.Text = $"{resourceName}: {current:F0}/{max:F0}";
        
        // Color coding for critical resources
        float percentage = current / max;
        if (percentage < 0.2f)
        {
            bar.Modulate = Colors.Red;
        }
        else if (percentage < 0.5f)
        {
            bar.Modulate = Colors.Yellow;
        }
        else
        {
            bar.Modulate = Colors.Green;
        }
    }

    private void OnDronesSelected(List<Drone> selectedDrones)
    {
        _selectedLabel.Text = $"Selected: {selectedDrones.Count}";
        
        // Update pattern button availability
        UpdatePatternButtons(selectedDrones);
    }

    private void UpdatePatternButtons(List<Drone> selectedDrones)
    {
        foreach (var kvp in _availablePatterns)
        {
            var pattern = kvp.Value;
            bool canExecute = pattern.CanExecute(selectedDrones);
            
            Button button = kvp.Key switch
            {
                "Spiral" => _spiralButton,
                "Pincer" => _pincerButton,
                "Scatter" => _scatterButton,
                _ => null
            };
            
            if (button != null)
            {
                button.Disabled = !canExecute;
                
                // Update button text with effectiveness
                if (canExecute)
                {
                    float effectiveness = pattern.CalculateEffectiveness(selectedDrones);
                    button.Text = $"{pattern.Name} ({effectiveness:P0})";
                }
                else
                {
                    button.Text = $"{pattern.Name} (N/A)";
                }
            }
        }
    }

    private void OnSectorGenerated(SectorGenerator.Sector sector)
    {
        _sectorLabel.Text = $"Sector: ({sector.Position.X}, {sector.Position.Y}) Depth: {sector.Depth}";
        
        // Update drone count display
        UpdateDroneCount();
    }

    private void UpdateDroneCount()
    {
        if (_droneSwarmManager != null)
        {
            int currentDrones = _droneSwarmManager.GetDroneCount();
            int maxDrones = _droneSwarmManager.MaxDrones;
            _droneLabel.Text = $"Drones: {currentDrones}/{maxDrones}";
        }
    }

    private void ExecutePattern(string patternName)
    {
        if (!_availablePatterns.ContainsKey(patternName))
        {
            GD.PrintErr($"Pattern {patternName} not found");
            return;
        }
        
        var pattern = _availablePatterns[patternName];
        _droneSwarmManager?.ExecutePattern(pattern);
        
        GD.Print($"UI triggered pattern execution: {patternName}");
    }

    private void ShowGameOverUI()
    {
        // Create a simple game over dialog
        var dialog = new AcceptDialog();
        dialog.DialogText = "Game Over!\n\nYour mothership has been lost to the void.\nBut the patterns you discovered will live on...";
        dialog.Title = "VOID STALKER";
        
        // Add restart button
        dialog.AddButton("Restart", false, "restart");
        dialog.CustomAction += OnGameOverAction;
        
        AddChild(dialog);
        dialog.PopupCentered();
    }

    private void OnGameOverAction(StringName action)
    {
        if (action == "restart")
        {
            _gameManager?.RestartGame();
        }
    }

    public override void _Process(double delta)
    {
        // Update drone count periodically
        UpdateDroneCount();
    }

    // Debug methods
    public void _on_debug_spawn_drone_pressed()
    {
        _droneSwarmManager?.SpawnDrone(Drone.DroneType.Basic);
    }

    public void _on_debug_add_biomass_pressed()
    {
        _resourceManager?.AddResource(ResourceManager.ResourceType.Biomass, 50.0f);
    }

    public void _on_debug_start_hunt_pressed()
    {
        _gameManager?.ChangeState(GameManager.GameState.Hunting);
    }
}
