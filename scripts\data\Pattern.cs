using Godot;
using System;
using System.Collections.Generic;

[System.Serializable]
public partial class Pattern : Resource
{
    [Export] public string Name { get; set; } = "";
    [Export] public string Description { get; set; } = "";
    [Export] public Godot.Collections.Array<Vector2> MovementSequence { get; set; } = new();
    [Export] public Godot.Collections.Array<float> TimingSequence { get; set; } = new();
    [Export] public float EnergyCost { get; set; } = 10.0f;
    [Export] public float EffectivenessDecay { get; set; } = 0.0f; // How much effectiveness decreases per use
    [Export] public PatternType Type { get; set; } = PatternType.Lure;
    [Export] public int MinimumDrones { get; set; } = 1;
    [Export] public int OptimalDrones { get; set; } = 10;
    [Export] public Godot.Collections.Array<Drone.DroneType> RequiredDroneTypes { get; set; } = new();
    
    // Pattern effectiveness tracking
    [Export] public float BaseEffectiveness { get; set; } = 1.0f;
    [Export] public float CurrentEffectiveness { get; set; } = 1.0f;
    [Export] public int TimesUsed { get; set; } = 0;
    [Export] public int SuccessfulUses { get; set; } = 0;
    
    // Pattern metadata
    [Export] public bool IsUnlocked { get; set; } = true;
    [Export] public string UnlockCondition { get; set; } = "";
    [Export] public int UnlockCost { get; set; } = 0; // In void echoes or data cores

    public enum PatternType
    {
        Lure,        // Attracts beasts
        Distraction, // Confuses beasts
        Trap,        // Immobilizes beasts
        Attack,      // Direct damage
        Defensive,   // Protects drones
        Utility      // Special effects
    }

    public Pattern()
    {
        MovementSequence = new Godot.Collections.Array<Vector2>();
        TimingSequence = new Godot.Collections.Array<float>();
        RequiredDroneTypes = new Godot.Collections.Array<Drone.DroneType>();
    }

    public Pattern(string name, PatternType type) : this()
    {
        Name = name;
        Type = type;
        CurrentEffectiveness = BaseEffectiveness;
    }

    // Create a deep copy of the pattern
    public Pattern Clone()
    {
        var clone = new Pattern(Name, Type)
        {
            Description = Description,
            EnergyCost = EnergyCost,
            EffectivenessDecay = EffectivenessDecay,
            MinimumDrones = MinimumDrones,
            OptimalDrones = OptimalDrones,
            BaseEffectiveness = BaseEffectiveness,
            CurrentEffectiveness = CurrentEffectiveness,
            TimesUsed = TimesUsed,
            SuccessfulUses = SuccessfulUses,
            IsUnlocked = IsUnlocked,
            UnlockCondition = UnlockCondition,
            UnlockCost = UnlockCost
        };

        // Deep copy arrays
        foreach (var movement in MovementSequence)
        {
            clone.MovementSequence.Add(movement);
        }
        
        foreach (var timing in TimingSequence)
        {
            clone.TimingSequence.Add(timing);
        }
        
        foreach (var droneType in RequiredDroneTypes)
        {
            clone.RequiredDroneTypes.Add(droneType);
        }

        return clone;
    }

    public void RecordUse(bool successful)
    {
        TimesUsed++;
        if (successful)
        {
            SuccessfulUses++;
        }
        
        // Apply effectiveness decay
        CurrentEffectiveness = Mathf.Max(CurrentEffectiveness - EffectivenessDecay, 0.1f);
        
        GD.Print($"Pattern {Name} used. Success: {successful}. Effectiveness: {CurrentEffectiveness:F2}");
    }

    public float GetSuccessRate()
    {
        return TimesUsed > 0 ? (float)SuccessfulUses / TimesUsed : 0.0f;
    }

    public bool CanExecute(List<Drone> availableDrones)
    {
        if (!IsUnlocked) return false;
        if (availableDrones.Count < MinimumDrones) return false;
        
        // Check if we have required drone types
        if (RequiredDroneTypes.Count > 0)
        {
            var availableTypes = new HashSet<Drone.DroneType>();
            foreach (var drone in availableDrones)
            {
                availableTypes.Add(drone.GetDroneType());
            }
            
            foreach (var requiredType in RequiredDroneTypes)
            {
                if (!availableTypes.Contains(requiredType))
                {
                    return false;
                }
            }
        }
        
        return true;
    }

    public float CalculateEffectiveness(List<Drone> drones)
    {
        if (drones.Count == 0) return 0.0f;
        
        float effectiveness = CurrentEffectiveness;
        
        // Drone count modifier
        float droneCountModifier = 1.0f;
        if (drones.Count < MinimumDrones)
        {
            droneCountModifier = (float)drones.Count / MinimumDrones;
        }
        else if (drones.Count > OptimalDrones)
        {
            // Diminishing returns for too many drones
            float excess = drones.Count - OptimalDrones;
            droneCountModifier = 1.0f - (excess * 0.05f); // 5% penalty per excess drone
            droneCountModifier = Mathf.Max(droneCountModifier, 0.5f);
        }
        
        effectiveness *= droneCountModifier;
        
        // Drone energy modifier
        float totalEnergyPercent = 0.0f;
        foreach (var drone in drones)
        {
            totalEnergyPercent += drone.GetEnergyPercentage();
        }
        float avgEnergyPercent = totalEnergyPercent / drones.Count;
        effectiveness *= avgEnergyPercent;
        
        return Mathf.Max(effectiveness, 0.1f);
    }

    public Vector2 GetMovementAtTime(float time)
    {
        if (MovementSequence.Count == 0 || TimingSequence.Count == 0) return Vector2.Zero;
        
        // Find the appropriate movement step for the given time
        for (int i = 0; i < TimingSequence.Count; i++)
        {
            if (time <= TimingSequence[i])
            {
                return i < MovementSequence.Count ? MovementSequence[i] : Vector2.Zero;
            }
        }
        
        // Return the last movement if time exceeds all timing points
        return MovementSequence[MovementSequence.Count - 1];
    }

    public float GetTotalDuration()
    {
        return TimingSequence.Count > 0 ? TimingSequence[TimingSequence.Count - 1] : 0.0f;
    }

    public void ResetEffectiveness()
    {
        CurrentEffectiveness = BaseEffectiveness;
        TimesUsed = 0;
        SuccessfulUses = 0;
        GD.Print($"Pattern {Name} effectiveness reset");
    }

    // Validation methods
    public bool IsValid()
    {
        if (string.IsNullOrEmpty(Name)) return false;
        if (MovementSequence.Count == 0) return false;
        if (TimingSequence.Count != MovementSequence.Count) return false;
        if (EnergyCost < 0) return false;
        if (MinimumDrones < 1) return false;
        if (OptimalDrones < MinimumDrones) return false;
        
        // Check timing sequence is monotonically increasing
        for (int i = 1; i < TimingSequence.Count; i++)
        {
            if (TimingSequence[i] <= TimingSequence[i - 1])
            {
                return false;
            }
        }
        
        return true;
    }

    public string GetValidationErrors()
    {
        var errors = new List<string>();
        
        if (string.IsNullOrEmpty(Name))
            errors.Add("Pattern name is required");
            
        if (MovementSequence.Count == 0)
            errors.Add("Pattern must have at least one movement");
            
        if (TimingSequence.Count != MovementSequence.Count)
            errors.Add("Movement and timing sequences must have the same length");
            
        if (EnergyCost < 0)
            errors.Add("Energy cost cannot be negative");
            
        if (MinimumDrones < 1)
            errors.Add("Minimum drones must be at least 1");
            
        if (OptimalDrones < MinimumDrones)
            errors.Add("Optimal drones cannot be less than minimum drones");
        
        // Check timing sequence
        for (int i = 1; i < TimingSequence.Count; i++)
        {
            if (TimingSequence[i] <= TimingSequence[i - 1])
            {
                errors.Add($"Timing sequence must be increasing (step {i})");
                break;
            }
        }
        
        return string.Join("; ", errors);
    }

    // Static factory methods for common patterns
    public static Pattern CreateSpiralPattern()
    {
        var pattern = new Pattern("Spiral Lure", PatternType.Lure)
        {
            Description = "Drones move in a spiral pattern to attract beasts",
            EnergyCost = 15.0f,
            MinimumDrones = 5,
            OptimalDrones = 15,
            EffectivenessDecay = 0.02f
        };
        
        // Create spiral movement
        int steps = 8;
        float radius = 50.0f;
        for (int i = 0; i < steps; i++)
        {
            float angle = (i * 2 * Mathf.Pi) / steps;
            float currentRadius = radius * (i + 1) / steps;
            Vector2 movement = new Vector2(
                Mathf.Cos(angle) * currentRadius,
                Mathf.Sin(angle) * currentRadius
            );
            pattern.MovementSequence.Add(movement);
            pattern.TimingSequence.Add(i * 0.5f);
        }
        
        return pattern;
    }

    public static Pattern CreatePincerPattern()
    {
        var pattern = new Pattern("Pincer Trap", PatternType.Trap)
        {
            Description = "Drones surround the target from multiple sides",
            EnergyCost = 20.0f,
            MinimumDrones = 8,
            OptimalDrones = 20,
            EffectivenessDecay = 0.03f
        };
        
        // Create pincer movement
        pattern.MovementSequence.Add(new Vector2(-100, 0));   // Left flank
        pattern.MovementSequence.Add(new Vector2(100, 0));    // Right flank
        pattern.MovementSequence.Add(new Vector2(-50, -50));  // Close left
        pattern.MovementSequence.Add(new Vector2(50, -50));   // Close right
        pattern.MovementSequence.Add(new Vector2(0, -100));   // Center close
        
        pattern.TimingSequence.Add(0.0f);
        pattern.TimingSequence.Add(0.5f);
        pattern.TimingSequence.Add(1.0f);
        pattern.TimingSequence.Add(1.5f);
        pattern.TimingSequence.Add(2.0f);
        
        return pattern;
    }

    public static Pattern CreateScatterPattern()
    {
        var pattern = new Pattern("Scatter Distraction", PatternType.Distraction)
        {
            Description = "Drones spread out randomly to confuse beasts",
            EnergyCost = 8.0f,
            MinimumDrones = 3,
            OptimalDrones = 12,
            EffectivenessDecay = 0.01f
        };
        
        var random = new Random();
        for (int i = 0; i < 6; i++)
        {
            Vector2 randomMovement = new Vector2(
                (float)(random.NextDouble() - 0.5) * 200,
                (float)(random.NextDouble() - 0.5) * 200
            );
            pattern.MovementSequence.Add(randomMovement);
            pattern.TimingSequence.Add(i * 0.3f);
        }
        
        return pattern;
    }
}
