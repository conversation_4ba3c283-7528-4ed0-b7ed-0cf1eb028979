[gd_scene format=3 uid="uid://7vyd0qlkl62g"]

[node name="Main" type="Node2D"]

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(0.5, 0.5)

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="Container" type="Container" parent="."]
offset_left = 88.0
offset_top = 31.0
offset_right = 697.0
offset_bottom = 387.0


[node name="Camera2D" type="Camera2D" parent="."]

[node name="UI" type="CanvasLayer" parent="."]
script = ExtResource("1_mainui")
drone_scene = ExtResource("2_drone")
