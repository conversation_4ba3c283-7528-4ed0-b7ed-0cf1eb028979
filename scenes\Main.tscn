[gd_scene load_steps=4 format=3 uid="uid://bqxqhqxqhqxqh"]

[ext_resource type="Script" path="res://scripts/ui/MainUI.cs" id="1_1a2b3"]
[ext_resource type="PackedScene" path="res://scenes/entities/Drone.tscn" id="2_4d5e6"]

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.05, 0.05, 0.1, 1)

[node name="Main" type="Node2D"]

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(0.5, 0.5)

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_1")

[node name="UI" type="CanvasLayer" parent="."]
script = ExtResource("1_1a2b3")
drone_scene = ExtResource("2_4d5e6")

[node name="HUD" type="Control" parent="UI"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="ResourcePanel" type="Panel" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -120.0
offset_right = 300.0
offset_bottom = -10.0

[node name="ResourceContainer" type="VBoxContainer" parent="UI/HUD/ResourcePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="FuelBar" type="ProgressBar" parent="UI/HUD/ResourcePanel/ResourceContainer"]
layout_mode = 2
max_value = 100.0
value = 100.0
show_percentage = false

[node name="FuelLabel" type="Label" parent="UI/HUD/ResourcePanel/ResourceContainer/FuelBar"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Fuel: 100/100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LifeSupportBar" type="ProgressBar" parent="UI/HUD/ResourcePanel/ResourceContainer"]
layout_mode = 2
max_value = 100.0
value = 100.0
show_percentage = false

[node name="LifeSupportLabel" type="Label" parent="UI/HUD/ResourcePanel/ResourceContainer/LifeSupportBar"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Life Support: 100/100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HullBar" type="ProgressBar" parent="UI/HUD/ResourcePanel/ResourceContainer"]
layout_mode = 2
max_value = 100.0
value = 100.0
show_percentage = false

[node name="HullLabel" type="Label" parent="UI/HUD/ResourcePanel/ResourceContainer/HullBar"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "Hull: 100/100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BiomassLabel" type="Label" parent="UI/HUD/ResourcePanel/ResourceContainer"]
layout_mode = 2
text = "Biomass: 0"

[node name="InfoPanel" type="Panel" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -300.0
offset_top = -120.0
offset_right = -10.0
offset_bottom = -10.0

[node name="InfoContainer" type="VBoxContainer" parent="UI/HUD/InfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="SectorLabel" type="Label" parent="UI/HUD/InfoPanel/InfoContainer"]
layout_mode = 2
text = "Sector: (0, 0) Depth: 1"

[node name="DroneLabel" type="Label" parent="UI/HUD/InfoPanel/InfoContainer"]
layout_mode = 2
text = "Drones: 20/100"

[node name="SelectedLabel" type="Label" parent="UI/HUD/InfoPanel/InfoContainer"]
layout_mode = 2
text = "Selected: 0"

[node name="GameStateLabel" type="Label" parent="UI/HUD/InfoPanel/InfoContainer"]
layout_mode = 2
text = "State: In Sector"

[node name="PatternPanel" type="Panel" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = 200.0

[node name="PatternContainer" type="VBoxContainer" parent="UI/HUD/PatternPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="PatternTitle" type="Label" parent="UI/HUD/PatternPanel/PatternContainer"]
layout_mode = 2
text = "Patterns"
horizontal_alignment = 1

[node name="SpiralButton" type="Button" parent="UI/HUD/PatternPanel/PatternContainer"]
layout_mode = 2
text = "Spiral Lure"

[node name="PincerButton" type="Button" parent="UI/HUD/PatternPanel/PatternContainer"]
layout_mode = 2
text = "Pincer Trap"

[node name="ScatterButton" type="Button" parent="UI/HUD/PatternPanel/PatternContainer"]
layout_mode = 2
text = "Scatter Distraction"

[node name="ControlsPanel" type="Panel" parent="UI/HUD"]
layout_mode = 1
anchors_preset = 0
offset_left = 10.0
offset_top = 10.0
offset_right = 300.0
offset_bottom = 100.0

[node name="ControlsContainer" type="VBoxContainer" parent="UI/HUD/ControlsPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="ControlsTitle" type="Label" parent="UI/HUD/ControlsPanel/ControlsContainer"]
layout_mode = 2
text = "Controls"
horizontal_alignment = 1

[node name="ControlsText" type="Label" parent="UI/HUD/ControlsPanel/ControlsContainer"]
layout_mode = 2
text = "Left Click: Select | Right Click: Move
Ctrl+Click: Multi-select | 1-9: Control Groups
Ctrl+1-9: Assign Groups | ESC: Pause"
autowrap_mode = 2
