using Godot;
using System;
using System.Collections.Generic;

public partial class ResourceManager : Node
{
    [Signal]
    public delegate void ResourceChangedEventHandler(ResourceType type, float currentAmount, float maxAmount);
    
    [Signal]
    public delegate void ResourceDepletedEventHandler(ResourceType type);
    
    [Signal]
    public delegate void CriticalResourceEventHandler(ResourceType type);

    public enum ResourceType
    {
        Fuel,
        LifeSupport,
        Hull,
        Biomass,
        QuantumCores
    }

    // Resource data structure
    private class ResourceData
    {
        public float Current { get; set; }
        public float Maximum { get; set; }
        public float DrainRate { get; set; }
        public float CriticalThreshold { get; set; }
        public bool IsCritical { get; set; }
        public bool IsDepleted { get; set; }

        public ResourceData(float max, float drainRate = 0.0f, float criticalPercent = 0.2f)
        {
            Maximum = max;
            Current = max;
            DrainRate = drainRate;
            CriticalThreshold = max * criticalPercent;
            IsCritical = false;
            IsDepleted = false;
        }
    }

    private Dictionary<ResourceType, ResourceData> _resources;
    private Timer _drainTimer;
    private bool _drainPaused = false;

    // Starting resource amounts
    private const float STARTING_FUEL = 100.0f;
    private const float STARTING_LIFE_SUPPORT = 100.0f;
    private const float STARTING_HULL = 100.0f;
    private const float STARTING_BIOMASS = 0.0f;
    private const float STARTING_QUANTUM_CORES = 0.0f;

    public override void _Ready()
    {
        InitializeResources();
        SetupDrainTimer();
        GD.Print("ResourceManager initialized");
    }

    private void InitializeResources()
    {
        _resources = new Dictionary<ResourceType, ResourceData>
        {
            [ResourceType.Fuel] = new ResourceData(STARTING_FUEL, 0.1f), // Slow constant drain
            [ResourceType.LifeSupport] = new ResourceData(STARTING_LIFE_SUPPORT, 0.05f), // Very slow drain
            [ResourceType.Hull] = new ResourceData(STARTING_HULL, 0.0f), // No passive drain
            [ResourceType.Biomass] = new ResourceData(1000.0f, 0.0f), // Large capacity, no drain
            [ResourceType.QuantumCores] = new ResourceData(100.0f, 0.0f) // Rare resource
        };

        // Set starting amounts
        _resources[ResourceType.Biomass].Current = STARTING_BIOMASS;
        _resources[ResourceType.QuantumCores].Current = STARTING_QUANTUM_CORES;
    }

    private void SetupDrainTimer()
    {
        _drainTimer = new Timer();
        _drainTimer.WaitTime = 1.0f; // Drain every second
        _drainTimer.Timeout += OnDrainTimerTimeout;
        AddChild(_drainTimer);
        _drainTimer.Start();
    }

    private void OnDrainTimerTimeout()
    {
        if (_drainPaused) return;

        foreach (var kvp in _resources)
        {
            var resourceType = kvp.Key;
            var resourceData = kvp.Value;

            if (resourceData.DrainRate > 0 && !resourceData.IsDepleted)
            {
                ConsumeResource(resourceType, resourceData.DrainRate);
            }
        }
    }

    public float GetResource(ResourceType type)
    {
        return _resources.ContainsKey(type) ? _resources[type].Current : 0.0f;
    }

    public float GetMaxResource(ResourceType type)
    {
        return _resources.ContainsKey(type) ? _resources[type].Maximum : 0.0f;
    }

    public float GetResourcePercentage(ResourceType type)
    {
        if (!_resources.ContainsKey(type)) return 0.0f;
        var data = _resources[type];
        return data.Maximum > 0 ? data.Current / data.Maximum : 0.0f;
    }

    public bool AddResource(ResourceType type, float amount)
    {
        if (!_resources.ContainsKey(type) || amount <= 0) return false;

        var data = _resources[type];
        var oldAmount = data.Current;
        data.Current = Mathf.Min(data.Current + amount, data.Maximum);

        // Check if resource is no longer depleted
        if (data.IsDepleted && data.Current > 0)
        {
            data.IsDepleted = false;
        }

        // Check if resource is no longer critical
        if (data.IsCritical && data.Current > data.CriticalThreshold)
        {
            data.IsCritical = false;
        }

        EmitSignal(SignalName.ResourceChanged, (int)type, data.Current, data.Maximum);
        
        GD.Print($"Added {amount} {type}. Total: {data.Current}/{data.Maximum}");
        return true;
    }

    public bool ConsumeResource(ResourceType type, float amount)
    {
        if (!_resources.ContainsKey(type) || amount <= 0) return false;

        var data = _resources[type];
        var oldAmount = data.Current;
        data.Current = Mathf.Max(data.Current - amount, 0.0f);

        // Check for critical threshold
        if (!data.IsCritical && data.Current <= data.CriticalThreshold && data.Current > 0)
        {
            data.IsCritical = true;
            EmitSignal(SignalName.CriticalResource, (int)type);
        }

        // Check for depletion
        if (!data.IsDepleted && data.Current <= 0)
        {
            data.IsDepleted = true;
            EmitSignal(SignalName.ResourceDepleted, (int)type);
        }

        EmitSignal(SignalName.ResourceChanged, (int)type, data.Current, data.Maximum);
        
        if (amount > 0.1f) // Don't log tiny drain amounts
        {
            GD.Print($"Consumed {amount} {type}. Remaining: {data.Current}/{data.Maximum}");
        }
        
        return true;
    }

    public bool HasSufficientResource(ResourceType type, float amount)
    {
        return _resources.ContainsKey(type) && _resources[type].Current >= amount;
    }

    public bool CanAfford(Dictionary<ResourceType, float> costs)
    {
        foreach (var cost in costs)
        {
            if (!HasSufficientResource(cost.Key, cost.Value))
                return false;
        }
        return true;
    }

    public bool SpendResources(Dictionary<ResourceType, float> costs)
    {
        if (!CanAfford(costs)) return false;

        foreach (var cost in costs)
        {
            ConsumeResource(cost.Key, cost.Value);
        }
        return true;
    }

    // Biomass conversion system
    public bool ConvertBiomass(ResourceType targetType, float biomassAmount)
    {
        if (!HasSufficientResource(ResourceType.Biomass, biomassAmount))
            return false;

        float conversionRate = GetBiomassConversionRate(targetType);
        float outputAmount = biomassAmount * conversionRate;

        if (ConsumeResource(ResourceType.Biomass, biomassAmount))
        {
            AddResource(targetType, outputAmount);
            GD.Print($"Converted {biomassAmount} biomass to {outputAmount} {targetType}");
            return true;
        }

        return false;
    }

    private float GetBiomassConversionRate(ResourceType targetType)
    {
        return targetType switch
        {
            ResourceType.Fuel => 0.8f,
            ResourceType.LifeSupport => 0.6f,
            ResourceType.Hull => 0.4f, // Hull repairs are expensive
            _ => 0.1f
        };
    }

    public void SetResourceDrainPaused(bool paused)
    {
        _drainPaused = paused;
        GD.Print($"Resource drain {(paused ? "paused" : "resumed")}");
    }

    public void ResetResources()
    {
        InitializeResources();
        GD.Print("Resources reset to starting values");
        
        // Emit signals for UI updates
        foreach (var kvp in _resources)
        {
            EmitSignal(SignalName.ResourceChanged, (int)kvp.Key, kvp.Value.Current, kvp.Value.Maximum);
        }
    }

    public void UpgradeResourceCapacity(ResourceType type, float additionalCapacity)
    {
        if (!_resources.ContainsKey(type)) return;

        var data = _resources[type];
        data.Maximum += additionalCapacity;
        
        // Update critical threshold
        data.CriticalThreshold = data.Maximum * 0.2f;
        
        EmitSignal(SignalName.ResourceChanged, (int)type, data.Current, data.Maximum);
        GD.Print($"Upgraded {type} capacity by {additionalCapacity}. New max: {data.Maximum}");
    }

    public Dictionary<ResourceType, float> GetAllResourceLevels()
    {
        var levels = new Dictionary<ResourceType, float>();
        foreach (var kvp in _resources)
        {
            levels[kvp.Key] = kvp.Value.Current;
        }
        return levels;
    }

    public bool IsResourceCritical(ResourceType type)
    {
        return _resources.ContainsKey(type) && _resources[type].IsCritical;
    }

    public bool IsResourceDepleted(ResourceType type)
    {
        return _resources.ContainsKey(type) && _resources[type].IsDepleted;
    }

    // Emergency actions
    public void EmergencyRefuel(float amount)
    {
        // Emergency refuel at cost of quantum cores
        float coreCost = amount * 0.1f;
        if (HasSufficientResource(ResourceType.QuantumCores, coreCost))
        {
            ConsumeResource(ResourceType.QuantumCores, coreCost);
            AddResource(ResourceType.Fuel, amount);
            GD.Print($"Emergency refuel: {amount} fuel for {coreCost} quantum cores");
        }
    }

    public void EmergencyRepair(float hullAmount)
    {
        // Emergency hull repair using biomass
        float biomassCost = hullAmount * 2.5f; // Expensive emergency repair
        if (HasSufficientResource(ResourceType.Biomass, biomassCost))
        {
            ConsumeResource(ResourceType.Biomass, biomassCost);
            AddResource(ResourceType.Hull, hullAmount);
            GD.Print($"Emergency repair: {hullAmount} hull for {biomassCost} biomass");
        }
    }
}
