<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="voidGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f0f23;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Void background -->
  <rect width="128" height="128" fill="url(#voidGradient)"/>
  
  <!-- Stars -->
  <circle cx="20" cy="20" r="1" fill="#ffffff" opacity="0.8"/>
  <circle cx="100" cy="30" r="1" fill="#ffffff" opacity="0.6"/>
  <circle cx="110" cy="80" r="1" fill="#ffffff" opacity="0.9"/>
  <circle cx="30" cy="100" r="1" fill="#ffffff" opacity="0.7"/>
  <circle cx="80" cy="15" r="1" fill="#ffffff" opacity="0.5"/>
  
  <!-- Central void beast (abstract) -->
  <ellipse cx="64" cy="64" rx="25" ry="15" fill="#2d1b69" opacity="0.8"/>
  <ellipse cx="64" cy="64" rx="20" ry="12" fill="#4a2c85" opacity="0.6"/>
  
  <!-- Drone swarm (small triangles) -->
  <polygon points="45,45 48,42 42,42" fill="#00ffff" opacity="0.9"/>
  <polygon points="85,50 88,47 82,47" fill="#00ffff" opacity="0.8"/>
  <polygon points="50,85 53,82 47,82" fill="#00ffff" opacity="0.7"/>
  <polygon points="80,80 83,77 77,77" fill="#00ffff" opacity="0.9"/>
  <polygon points="35,65 38,62 32,62" fill="#00ffff" opacity="0.6"/>
  <polygon points="95,65 98,62 92,62" fill="#00ffff" opacity="0.8"/>
  
  <!-- Pattern lines (connecting drones) -->
  <line x1="45" y1="45" x2="85" y2="50" stroke="#00ffff" stroke-width="1" opacity="0.4"/>
  <line x1="85" y1="50" x2="80" y2="80" stroke="#00ffff" stroke-width="1" opacity="0.4"/>
  <line x1="80" y1="80" x2="50" y2="85" stroke="#00ffff" stroke-width="1" opacity="0.4"/>
  <line x1="50" y1="85" x2="35" y2="65" stroke="#00ffff" stroke-width="1" opacity="0.4"/>
  
  <!-- Title text -->
  <text x="64" y="115" font-family="monospace" font-size="8" fill="#ffffff" text-anchor="middle" opacity="0.8">VOID STALKER</text>
</svg>
