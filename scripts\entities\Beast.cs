using Godot;
using System;
using System.Collections.Generic;

public partial class Beast : CharacterBody2D
{
    [Signal]
    public delegate void BeastDestroyedEvent<PERSON>andler(Beast beast, Dictionary<string, float> resources);
    
    [Signal]
    public delegate void BeastAttackedDroneEventHandler(Beast beast, Drone drone);

    public enum BeastType
    {
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        QuantumWraith,
        PlasmaLeech,
        GravityMaw,
        PhaseSpider,
        NebulaDrifter,
        CrystalSwarm,
        DarkMatterEntity
    }

    public enum BeastState
    {
        Idle,
        Investigating,
        Hunting,
        Attacking,
        Fleeing,
        Dead
    }

    [Export] public BeastType Type { get; set; } = BeastType.VoidLurker;
    [Export] public float MaxHealth { get; set; } = 150.0f;
    [Export] public float MaxSpeed { get; set; } = 120.0f;
    [Export] public float DetectionRange { get; set; } = 200.0f;
    [Export] public float AttackRange { get; set; } = 50.0f;
    [Export] public float AttackDamage { get; set; } = 25.0f;
    [Export] public float SuspicionThreshold { get; set; } = 0.7f;

    public BeastState CurrentState { get; private set; } = BeastState.Idle;
    public float CurrentHealth { get; private set; }
    public float Suspicion { get; private set; } = 0.0f;
    public Vector2 HomePosition { get; private set; }
    
    // AI and behavior
    private NavigationAgent2D _navigationAgent;
    private Area2D _detectionArea;
    private Area2D _attackArea;
    private Timer _behaviorTimer;
    private Timer _attackTimer;
    
    // Visual components
    private Sprite2D _sprite;
    private AnimationPlayer _animationPlayer;
    
    // Targets and memory
    private List<Drone> _detectedDrones = new List<Drone>();
    private Drone _currentTarget;
    private Vector2 _lastKnownTargetPosition;
    private float _timeSinceLastSighting = 0.0f;
    
    // Pattern resistance (learned from evolution system)
    private Dictionary<string, float> _patternResistances = new Dictionary<string, float>();
    
    // Loot
    private Dictionary<string, float> _lootTable = new Dictionary<string, float>();

    public override void _Ready()
    {
        CurrentHealth = MaxHealth;
        HomePosition = GlobalPosition;
        
        SetupComponents();
        SetupBehaviorTimers();
        InitializeLootTable();
        LoadPatternResistances();
        
        GD.Print($"Beast {Type} spawned at {GlobalPosition}");
    }

    private void SetupComponents()
    {
        // Navigation
        _navigationAgent = new NavigationAgent2D();
        _navigationAgent.PathDesiredDistance = 8.0f;
        _navigationAgent.TargetDesiredDistance = 8.0f;
        AddChild(_navigationAgent);
        
        // Sprite
        _sprite = new Sprite2D();
        _sprite.Modulate = GetBeastColor();
        _sprite.Scale = Vector2.One * 2.0f; // Make beasts larger than drones
        AddChild(_sprite);
        
        // Animation
        _animationPlayer = new AnimationPlayer();
        AddChild(_animationPlayer);
        
        // Detection area
        _detectionArea = new Area2D();
        var detectionCollision = new CollisionShape2D();
        var detectionShape = new CircleShape2D();
        detectionShape.Radius = DetectionRange;
        detectionCollision.Shape = detectionShape;
        _detectionArea.AddChild(detectionCollision);
        AddChild(_detectionArea);
        
        // Attack area
        _attackArea = new Area2D();
        var attackCollision = new CollisionShape2D();
        var attackShape = new CircleShape2D();
        attackShape.Radius = AttackRange;
        attackCollision.Shape = attackShape;
        _attackArea.AddChild(attackCollision);
        AddChild(_attackArea);
        
        // Set collision layers
        _detectionArea.CollisionMask = 1; // Detect drones
        _attackArea.CollisionMask = 1; // Attack drones
        
        // Connect signals
        _detectionArea.BodyEntered += OnDroneDetected;
        _detectionArea.BodyExited += OnDroneLost;
        _attackArea.BodyEntered += OnDroneInAttackRange;
    }

    private void SetupBehaviorTimers()
    {
        _behaviorTimer = new Timer();
        _behaviorTimer.WaitTime = 1.0f; // Update behavior every second
        _behaviorTimer.Timeout += UpdateBehavior;
        AddChild(_behaviorTimer);
        _behaviorTimer.Start();
        
        _attackTimer = new Timer();
        _attackTimer.WaitTime = 2.0f; // Attack every 2 seconds
        _attackTimer.Timeout += PerformAttack;
        AddChild(_attackTimer);
    }

    private void InitializeLootTable()
    {
        // Base loot for all beasts
        _lootTable["Biomass"] = 15.0f + (float)Type * 5.0f; // More advanced beasts give more biomass
        
        // Special loot based on beast type
        switch (Type)
        {
            case BeastType.QuantumWraith:
                _lootTable["QuantumCores"] = 1.0f;
                break;
            case BeastType.CrystalSwarm:
                _lootTable["Biomass"] *= 1.5f; // Crystal swarms are rich in biomass
                break;
            case BeastType.DarkMatterEntity:
                _lootTable["QuantumCores"] = 2.0f;
                _lootTable["Fuel"] = 10.0f; // Dark matter can be converted to fuel
                break;
        }
    }

    private void LoadPatternResistances()
    {
        var evolutionDB = GetNode<BeastEvolutionDB>("/root/BeastEvolutionDB");
        if (evolutionDB != null)
        {
            _patternResistances = evolutionDB.GetBeastResistances(Type.ToString());
        }
    }

    private Color GetBeastColor()
    {
        return Type switch
        {
            BeastType.VoidLurker => Colors.Purple,
            BeastType.QuantumWraith => Colors.Cyan,
            BeastType.PlasmaLeech => Colors.Orange,
            BeastType.GravityMaw => Colors.Gray,
            BeastType.PhaseSpider => Colors.Magenta,
            BeastType.NebulaDrifter => Colors.Blue,
            BeastType.CrystalSwarm => Colors.Green,
            BeastType.DarkMatterEntity => Colors.Black,
            _ => Colors.Red
        };
    }

    public override void _PhysicsProcess(double delta)
    {
        if (CurrentState == BeastState.Dead) return;
        
        _timeSinceLastSighting += (float)delta;
        HandleMovement(delta);
        UpdateSuspicion(delta);
        UpdateVisuals();
    }

    private void HandleMovement(double delta)
    {
        Vector2 targetPosition = Vector2.Zero;
        
        switch (CurrentState)
        {
            case BeastState.Idle:
                // Patrol around home position
                if (_navigationAgent.IsNavigationFinished())
                {
                    var random = new Random();
                    Vector2 randomOffset = new Vector2(
                        (float)(random.NextDouble() - 0.5) * 100,
                        (float)(random.NextDouble() - 0.5) * 100
                    );
                    targetPosition = HomePosition + randomOffset;
                }
                break;
                
            case BeastState.Investigating:
                targetPosition = _lastKnownTargetPosition;
                break;
                
            case BeastState.Hunting:
                if (_currentTarget != null && IsInstanceValid(_currentTarget))
                {
                    targetPosition = _currentTarget.GlobalPosition;
                    _lastKnownTargetPosition = targetPosition;
                    _timeSinceLastSighting = 0.0f;
                }
                else
                {
                    targetPosition = _lastKnownTargetPosition;
                }
                break;
                
            case BeastState.Fleeing:
                // Move away from detected drones
                if (_detectedDrones.Count > 0)
                {
                    Vector2 fleeDirection = Vector2.Zero;
                    foreach (var drone in _detectedDrones)
                    {
                        if (IsInstanceValid(drone))
                        {
                            fleeDirection += (GlobalPosition - drone.GlobalPosition).Normalized();
                        }
                    }
                    targetPosition = GlobalPosition + fleeDirection.Normalized() * 200;
                }
                else
                {
                    targetPosition = HomePosition;
                }
                break;
        }
        
        if (targetPosition != Vector2.Zero)
        {
            _navigationAgent.TargetPosition = targetPosition;
        }
        
        // Move towards target
        if (!_navigationAgent.IsNavigationFinished())
        {
            Vector2 nextPosition = _navigationAgent.GetNextPathPosition();
            Vector2 direction = (nextPosition - GlobalPosition).Normalized();
            
            float currentSpeed = MaxSpeed;
            if (CurrentState == BeastState.Fleeing)
                currentSpeed *= 1.5f; // Flee faster
            else if (CurrentState == BeastState.Investigating)
                currentSpeed *= 0.7f; // Investigate slower
            
            Velocity = direction * currentSpeed;
            MoveAndSlide();
        }
    }

    private void UpdateBehavior()
    {
        switch (CurrentState)
        {
            case BeastState.Idle:
                if (_detectedDrones.Count > 0)
                {
                    SetState(BeastState.Investigating);
                }
                break;
                
            case BeastState.Investigating:
                if (Suspicion >= SuspicionThreshold)
                {
                    if (_detectedDrones.Count > 0)
                    {
                        _currentTarget = GetClosestDrone();
                        SetState(BeastState.Hunting);
                    }
                    else
                    {
                        SetState(BeastState.Fleeing);
                    }
                }
                else if (_detectedDrones.Count == 0 && _timeSinceLastSighting > 5.0f)
                {
                    SetState(BeastState.Idle);
                }
                break;
                
            case BeastState.Hunting:
                if (_currentTarget == null || !IsInstanceValid(_currentTarget))
                {
                    _currentTarget = GetClosestDrone();
                    if (_currentTarget == null)
                    {
                        SetState(BeastState.Investigating);
                    }
                }
                break;
                
            case BeastState.Attacking:
                if (_detectedDrones.Count == 0)
                {
                    SetState(BeastState.Investigating);
                }
                break;
                
            case BeastState.Fleeing:
                if (_detectedDrones.Count == 0 && _timeSinceLastSighting > 3.0f)
                {
                    SetState(BeastState.Idle);
                    Suspicion = Mathf.Max(Suspicion - 0.1f, 0.0f); // Reduce suspicion when safe
                }
                break;
        }
    }

    private void UpdateSuspicion(double delta)
    {
        float suspicionChange = 0.0f;
        
        if (_detectedDrones.Count > 0)
        {
            // Increase suspicion based on number of drones and their behavior
            suspicionChange = _detectedDrones.Count * 0.1f * (float)delta;
            
            // Patterns increase suspicion faster
            foreach (var drone in _detectedDrones)
            {
                if (drone.CurrentState == Drone.DroneState.ExecutingPattern)
                {
                    suspicionChange += 0.2f * (float)delta;
                }
            }
        }
        else
        {
            // Slowly decrease suspicion when no drones are detected
            suspicionChange = -0.05f * (float)delta;
        }
        
        Suspicion = Mathf.Clamp(Suspicion + suspicionChange, 0.0f, 1.0f);
    }

    private Drone GetClosestDrone()
    {
        Drone closest = null;
        float closestDistance = float.MaxValue;
        
        foreach (var drone in _detectedDrones)
        {
            if (IsInstanceValid(drone))
            {
                float distance = GlobalPosition.DistanceTo(drone.GlobalPosition);
                if (distance < closestDistance)
                {
                    closest = drone;
                    closestDistance = distance;
                }
            }
        }
        
        return closest;
    }

    private void SetState(BeastState newState)
    {
        if (CurrentState == newState) return;
        
        var oldState = CurrentState;
        CurrentState = newState;
        
        // Handle state transitions
        switch (newState)
        {
            case BeastState.Attacking:
                _attackTimer.Start();
                break;
            case BeastState.Fleeing:
                _attackTimer.Stop();
                break;
        }
        
        GD.Print($"Beast {Type} state: {oldState} -> {newState} (Suspicion: {Suspicion:F2})");
    }

    private void OnDroneDetected(Node2D body)
    {
        if (body is Drone drone && !_detectedDrones.Contains(drone))
        {
            _detectedDrones.Add(drone);
            GD.Print($"Beast {Type} detected drone");
        }
    }

    private void OnDroneLost(Node2D body)
    {
        if (body is Drone drone)
        {
            _detectedDrones.Remove(drone);
        }
    }

    private void OnDroneInAttackRange(Node2D body)
    {
        if (body is Drone drone && CurrentState == BeastState.Hunting)
        {
            SetState(BeastState.Attacking);
        }
    }

    private void PerformAttack()
    {
        if (CurrentState != BeastState.Attacking) return;
        
        var dronesInRange = _attackArea.GetOverlappingBodies();
        foreach (var body in dronesInRange)
        {
            if (body is Drone drone)
            {
                drone.TakeDamage(AttackDamage);
                EmitSignal(SignalName.BeastAttackedDrone, this, drone);
                GD.Print($"Beast {Type} attacked drone for {AttackDamage} damage");
                break; // Attack one drone at a time
            }
        }
    }

    public void TakeDamage(float damage)
    {
        CurrentHealth = Mathf.Max(CurrentHealth - damage, 0.0f);
        
        if (CurrentHealth <= 0)
        {
            Die();
        }
        else
        {
            // Increase suspicion when taking damage
            Suspicion = Mathf.Min(Suspicion + 0.3f, 1.0f);
            
            // Visual damage feedback
            PlayDamageAnimation();
        }
    }

    private void Die()
    {
        SetState(BeastState.Dead);
        EmitSignal(SignalName.BeastDestroyed, this, _lootTable);
        
        // Play death animation
        PlayDeathAnimation();
        
        // Remove after animation
        var timer = GetTree().CreateTimer(2.0f);
        timer.Timeout += () => QueueFree();
        
        GD.Print($"Beast {Type} destroyed - dropped {_lootTable.Count} resource types");
    }

    private void UpdateVisuals()
    {
        // Update sprite based on state
        Color baseColor = GetBeastColor();
        
        switch (CurrentState)
        {
            case BeastState.Investigating:
                _sprite.Modulate = baseColor.Lerp(Colors.Yellow, 0.3f);
                break;
            case BeastState.Hunting:
                _sprite.Modulate = baseColor.Lerp(Colors.Red, 0.5f);
                break;
            case BeastState.Attacking:
                _sprite.Modulate = Colors.Red;
                break;
            case BeastState.Fleeing:
                _sprite.Modulate = baseColor.Lerp(Colors.Blue, 0.3f);
                break;
            default:
                _sprite.Modulate = baseColor;
                break;
        }
        
        // Scale based on health
        float healthPercent = CurrentHealth / MaxHealth;
        _sprite.Scale = Vector2.One * (1.5f + healthPercent * 0.5f);
    }

    private void PlayDamageAnimation()
    {
        var tween = CreateTween();
        tween.TweenProperty(_sprite, "modulate", Colors.White, 0.1f);
        tween.TweenProperty(_sprite, "modulate", GetBeastColor(), 0.1f);
    }

    private void PlayDeathAnimation()
    {
        var tween = CreateTween();
        tween.Parallel().TweenProperty(_sprite, "scale", Vector2.Zero, 1.0f);
        tween.Parallel().TweenProperty(_sprite, "modulate:a", 0.0f, 1.0f);
    }

    // Public getters
    public float GetHealthPercentage() => CurrentHealth / MaxHealth;
    public float GetSuspicionLevel() => Suspicion;
    public BeastType GetBeastType() => Type;
    public Dictionary<string, float> GetLootTable() => new Dictionary<string, float>(_lootTable);
}
