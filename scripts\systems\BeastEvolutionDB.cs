using Godot;
using System;
using System.Collections.Generic;

public partial class BeastEvolutionDB : Node
{
    [Signal]
    public delegate void BeastEvolutionUpdatedEventHandler(string beastType, string patternId, float newResistance);

    private Dictionary<string, Dictionary<string, float>> _beastResistances = new();
    private Dictionary<string, int> _encounterCounts = new();
    private Dictionary<string, DateTime> _lastUpdated = new();
    
    private const string SAVE_FILE_PATH = "user://beast_evolution.save";
    private const float MAX_RESISTANCE = 0.95f; // Beasts can't become completely immune
    private const float RESISTANCE_INCREMENT = 0.05f; // How much resistance increases per failed hunt

    public override void _Ready()
    {
        LoadEvolutionData();
        GD.Print("BeastEvolutionDB initialized");
    }

    public void RecordPatternUse(string beastType, string patternId, bool successful)
    {
        // Initialize beast data if not exists
        if (!_beastResistances.ContainsKey(beastType))
        {
            _beastResistances[beastType] = new Dictionary<string, float>();
        }
        
        if (!_beastResistances[beastType].ContainsKey(patternId))
        {
            _beastResistances[beastType][patternId] = 0.0f;
        }
        
        // Update encounter count
        string key = $"{beastType}_{patternId}";
        _encounterCounts[key] = _encounterCounts.GetValueOrDefault(key, 0) + 1;
        _lastUpdated[key] = DateTime.Now;
        
        // Update resistance based on success/failure
        if (!successful)
        {
            float currentResistance = _beastResistances[beastType][patternId];
            float newResistance = Mathf.Min(currentResistance + RESISTANCE_INCREMENT, MAX_RESISTANCE);
            _beastResistances[beastType][patternId] = newResistance;
            
            EmitSignal(SignalName.BeastEvolutionUpdated, beastType, patternId, newResistance);
            
            GD.Print($"Beast {beastType} developed resistance to {patternId}: {newResistance:F2}");
        }
        else
        {
            // Successful patterns might slightly reduce resistance over time (very slowly)
            float currentResistance = _beastResistances[beastType][patternId];
            float newResistance = Mathf.Max(currentResistance - (RESISTANCE_INCREMENT * 0.1f), 0.0f);
            _beastResistances[beastType][patternId] = newResistance;
        }
        
        // Auto-save evolution data
        SaveEvolutionData();
    }

    public float GetPatternResistance(string beastType, string patternId)
    {
        if (!_beastResistances.ContainsKey(beastType))
            return 0.0f;
            
        return _beastResistances[beastType].GetValueOrDefault(patternId, 0.0f);
    }

    public Dictionary<string, float> GetBeastResistances(string beastType)
    {
        if (!_beastResistances.ContainsKey(beastType))
            return new Dictionary<string, float>();
            
        return new Dictionary<string, float>(_beastResistances[beastType]);
    }

    public void RecordFailedHunt()
    {
        // This would be called when a hunt fails - for now just log it
        GD.Print("Failed hunt recorded - beasts learn from player patterns");
    }

    private void SaveEvolutionData()
    {
        var saveData = new Dictionary<string, Variant>
        {
            ["resistances"] = ConvertResistancesToVariant(),
            ["encounters"] = ConvertEncountersToVariant(),
            ["last_updated"] = DateTime.Now.ToBinary()
        };
        
        var saveFile = FileAccess.Open(SAVE_FILE_PATH, FileAccess.ModeFlags.Write);
        if (saveFile != null)
        {
            saveFile.StoreString(Json.Stringify(saveData));
            saveFile.Close();
            GD.Print("Beast evolution data saved");
        }
        else
        {
            GD.PrintErr("Failed to save beast evolution data");
        }
    }

    private void LoadEvolutionData()
    {
        if (!FileAccess.FileExists(SAVE_FILE_PATH))
        {
            GD.Print("No existing beast evolution data found");
            return;
        }
        
        var saveFile = FileAccess.Open(SAVE_FILE_PATH, FileAccess.ModeFlags.Read);
        if (saveFile == null)
        {
            GD.PrintErr("Failed to load beast evolution data");
            return;
        }
        
        string jsonString = saveFile.GetAsText();
        saveFile.Close();
        
        var json = new Json();
        var parseResult = json.Parse(jsonString);
        
        if (parseResult != Error.Ok)
        {
            GD.PrintErr("Failed to parse beast evolution data");
            return;
        }
        
        var saveData = json.Data.AsGodotDictionary();
        
        if (saveData.ContainsKey("resistances"))
        {
            LoadResistancesFromVariant(saveData["resistances"]);
        }
        
        if (saveData.ContainsKey("encounters"))
        {
            LoadEncountersFromVariant(saveData["encounters"]);
        }
        
        GD.Print("Beast evolution data loaded");
    }

    private Variant ConvertResistancesToVariant()
    {
        var result = new Godot.Collections.Dictionary();
        
        foreach (var beastKvp in _beastResistances)
        {
            var patternDict = new Godot.Collections.Dictionary();
            foreach (var patternKvp in beastKvp.Value)
            {
                patternDict[patternKvp.Key] = patternKvp.Value;
            }
            result[beastKvp.Key] = patternDict;
        }
        
        return result;
    }

    private void LoadResistancesFromVariant(Variant data)
    {
        _beastResistances.Clear();
        
        var dict = data.AsGodotDictionary();
        foreach (var beastKvp in dict)
        {
            string beastType = beastKvp.Key.AsString();
            var patternDict = beastKvp.Value.AsGodotDictionary();
            
            _beastResistances[beastType] = new Dictionary<string, float>();
            
            foreach (var patternKvp in patternDict)
            {
                string patternId = patternKvp.Key.AsString();
                float resistance = patternKvp.Value.AsSingle();
                _beastResistances[beastType][patternId] = resistance;
            }
        }
    }

    private Variant ConvertEncountersToVariant()
    {
        var result = new Godot.Collections.Dictionary();
        foreach (var kvp in _encounterCounts)
        {
            result[kvp.Key] = kvp.Value;
        }
        return result;
    }

    private void LoadEncountersFromVariant(Variant data)
    {
        _encounterCounts.Clear();
        
        var dict = data.AsGodotDictionary();
        foreach (var kvp in dict)
        {
            _encounterCounts[kvp.Key.AsString()] = kvp.Value.AsInt32();
        }
    }

    // Reset evolution for testing or special events
    public void ResetBeastEvolution(string beastType = null)
    {
        if (beastType == null)
        {
            _beastResistances.Clear();
            _encounterCounts.Clear();
            _lastUpdated.Clear();
            GD.Print("All beast evolution data reset");
        }
        else if (_beastResistances.ContainsKey(beastType))
        {
            _beastResistances[beastType].Clear();
            GD.Print($"Evolution data reset for {beastType}");
        }
        
        SaveEvolutionData();
    }

    // Get analytics data
    public Dictionary<string, object> GetEvolutionAnalytics()
    {
        var analytics = new Dictionary<string, object>();
        
        int totalBeastTypes = _beastResistances.Count;
        int totalPatterns = 0;
        float avgResistance = 0.0f;
        int totalEncounters = 0;
        
        foreach (var beastData in _beastResistances.Values)
        {
            totalPatterns += beastData.Count;
            foreach (var resistance in beastData.Values)
            {
                avgResistance += resistance;
            }
        }
        
        foreach (var encounters in _encounterCounts.Values)
        {
            totalEncounters += encounters;
        }
        
        if (totalPatterns > 0)
        {
            avgResistance /= totalPatterns;
        }
        
        analytics["total_beast_types"] = totalBeastTypes;
        analytics["total_pattern_resistances"] = totalPatterns;
        analytics["average_resistance"] = avgResistance;
        analytics["total_encounters"] = totalEncounters;
        
        return analytics;
    }

    // Check if a pattern is becoming ineffective
    public bool IsPatternOverused(string beastType, string patternId, float threshold = 0.7f)
    {
        float resistance = GetPatternResistance(beastType, patternId);
        return resistance >= threshold;
    }

    // Get recommendations for pattern usage
    public List<string> GetPatternRecommendations(string beastType, List<string> availablePatterns)
    {
        var recommendations = new List<string>();
        
        if (!_beastResistances.ContainsKey(beastType))
        {
            // No data for this beast type, all patterns are equally good
            return new List<string>(availablePatterns);
        }
        
        var beastData = _beastResistances[beastType];
        
        // Sort patterns by effectiveness (lowest resistance first)
        var sortedPatterns = availablePatterns
            .OrderBy(pattern => beastData.GetValueOrDefault(pattern, 0.0f))
            .ToList();
        
        return sortedPatterns;
    }
}
