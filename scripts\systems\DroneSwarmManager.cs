using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class DroneSwarmManager : Node
{
    [Signal]
    public delegate void DronesSelectedEventHandler(List<Drone> selectedDrones);
    
    [Signal]
    public delegate void AllDronesDestroyedEventHandler();
    
    [Signal]
    public delegate void PatternExecutedEventHandler(Pattern pattern, List<Drone> drones);

    private List<Drone> _allDrones = new List<Drone>();
    private List<Drone> _selectedDrones = new List<Drone>();
    private Dictionary<int, List<Drone>> _controlGroups = new Dictionary<int, List<Drone>>();
    
    // Selection box
    private bool _isSelecting = false;
    private Vector2 _selectionStart;
    private Vector2 _selectionEnd;
    private Control _selectionBox;
    
    // Formation system
    private Vector2 _formationCenter = Vector2.Zero;
    private float _formationSpacing = 40.0f;
    
    // Pattern execution
    private Pattern _queuedPattern;
    private bool _isExecutingPattern = false;
    
    // Drone spawning
    [Export] public PackedScene DroneScene { get; set; }
    [Export] public int MaxDrones { get; set; } = 100;
    [Export] public Vector2 SpawnArea { get; set; } = new Vector2(200, 200);

    public override void _Ready()
    {
        SetupSelectionBox();
        SpawnInitialDrones();
        GD.Print("DroneSwarmManager initialized");
    }

    public override void _Input(InputEvent @event)
    {
        HandleSelectionInput(@event);
        HandleControlGroupInput(@event);
        HandleCommandInput(@event);
    }

    private void SetupSelectionBox()
    {
        _selectionBox = new Control();
        _selectionBox.SetAnchorsAndOffsetsPreset(Control.PresetMode.FullRect);
        _selectionBox.MouseFilter = Control.MouseFilterEnum.Ignore;
        GetTree().CurrentScene.AddChild(_selectionBox);
        
        // Create visual for selection box
        _selectionBox.Draw += DrawSelectionBox;
    }

    private void SpawnInitialDrones()
    {
        // Load drone scene if not set
        if (DroneScene == null)
        {
            DroneScene = GD.Load<PackedScene>("res://scenes/entities/Drone.tscn");
        }
        
        // Spawn starting drones
        int startingDrones = 20;
        for (int i = 0; i < startingDrones; i++)
        {
            SpawnDrone(Drone.DroneType.Basic);
        }
    }

    public Drone SpawnDrone(Drone.DroneType type, Vector2? position = null)
    {
        if (_allDrones.Count >= MaxDrones)
        {
            GD.Print("Cannot spawn drone: Maximum capacity reached");
            return null;
        }
        
        if (DroneScene == null)
        {
            GD.PrintErr("DroneScene not set in DroneSwarmManager");
            return null;
        }
        
        var droneInstance = DroneScene.Instantiate<Drone>();
        droneInstance.Type = type;
        
        // Set spawn position
        Vector2 spawnPos = position ?? GetRandomSpawnPosition();
        droneInstance.GlobalPosition = spawnPos;
        
        // Add to scene
        GetTree().CurrentScene.AddChild(droneInstance);
        
        // Connect signals
        droneInstance.DroneDestroyed += OnDroneDestroyed;
        droneInstance.DroneSelected += OnDroneSelected;
        droneInstance.DroneDeselected += OnDroneDeselected;
        droneInstance.PatternComplete += OnDronePatternComplete;
        
        _allDrones.Add(droneInstance);
        
        GD.Print($"Spawned {type} drone at {spawnPos}. Total drones: {_allDrones.Count}");
        return droneInstance;
    }

    private Vector2 GetRandomSpawnPosition()
    {
        var random = new Random();
        return new Vector2(
            (float)(random.NextDouble() - 0.5) * SpawnArea.X,
            (float)(random.NextDouble() - 0.5) * SpawnArea.Y
        );
    }

    private void HandleSelectionInput(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseButton)
        {
            if (mouseButton.ButtonIndex == MouseButton.Left)
            {
                if (mouseButton.Pressed)
                {
                    StartSelection(mouseButton.GlobalPosition);
                }
                else
                {
                    EndSelection(mouseButton.GlobalPosition);
                }
            }
        }
        else if (@event is InputEventMouseMotion mouseMotion && _isSelecting)
        {
            UpdateSelection(mouseMotion.GlobalPosition);
        }
    }

    private void HandleControlGroupInput(InputEvent @event)
    {
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            // Control groups 1-9
            if (keyEvent.Keycode >= Key.Key1 && keyEvent.Keycode <= Key.Key9)
            {
                int groupNumber = (int)(keyEvent.Keycode - Key.Key1 + 1);
                
                if (Input.IsActionPressed("ui_accept")) // Ctrl held
                {
                    AssignControlGroup(groupNumber);
                }
                else
                {
                    SelectControlGroup(groupNumber);
                }
            }
        }
    }

    private void HandleCommandInput(InputEvent @event)
    {
        if (@event is InputEventMouseButton mouseButton && mouseButton.ButtonIndex == MouseButton.Right && mouseButton.Pressed)
        {
            if (_selectedDrones.Count > 0)
            {
                CommandSelectedDrones(mouseButton.GlobalPosition);
            }
        }
    }

    private void StartSelection(Vector2 position)
    {
        _isSelecting = true;
        _selectionStart = position;
        _selectionEnd = position;
        
        // Clear selection if not holding Ctrl
        if (!Input.IsActionPressed("ui_accept"))
        {
            ClearSelection();
        }
    }

    private void UpdateSelection(Vector2 position)
    {
        _selectionEnd = position;
        _selectionBox.QueueRedraw();
    }

    private void EndSelection(Vector2 position)
    {
        _isSelecting = false;
        _selectionEnd = position;
        
        // Select drones in box
        Rect2 selectionRect = new Rect2(_selectionStart, _selectionEnd - _selectionStart);
        selectionRect = selectionRect.Abs(); // Ensure positive size
        
        foreach (var drone in _allDrones)
        {
            if (selectionRect.HasPoint(drone.GlobalPosition))
            {
                AddToSelection(drone);
            }
        }
        
        _selectionBox.QueueRedraw();
    }

    public void HandleDroneClicked(Drone drone, bool addToSelection)
    {
        if (addToSelection)
        {
            if (_selectedDrones.Contains(drone))
            {
                RemoveFromSelection(drone);
            }
            else
            {
                AddToSelection(drone);
            }
        }
        else
        {
            ClearSelection();
            AddToSelection(drone);
        }
    }

    private void AddToSelection(Drone drone)
    {
        if (!_selectedDrones.Contains(drone))
        {
            _selectedDrones.Add(drone);
            drone.SetSelected(true);
        }
        
        EmitSignal(SignalName.DronesSelected, _selectedDrones);
    }

    private void RemoveFromSelection(Drone drone)
    {
        if (_selectedDrones.Remove(drone))
        {
            drone.SetSelected(false);
        }
        
        EmitSignal(SignalName.DronesSelected, _selectedDrones);
    }

    private void ClearSelection()
    {
        foreach (var drone in _selectedDrones)
        {
            drone.SetSelected(false);
        }
        _selectedDrones.Clear();
        
        EmitSignal(SignalName.DronesSelected, _selectedDrones);
    }

    private void CommandSelectedDrones(Vector2 targetPosition)
    {
        if (_selectedDrones.Count == 0) return;
        
        // Calculate formation positions
        var formationPositions = CalculateFormationPositions(targetPosition, _selectedDrones.Count);
        
        for (int i = 0; i < _selectedDrones.Count; i++)
        {
            _selectedDrones[i].MoveTo(formationPositions[i]);
        }
        
        GD.Print($"Commanded {_selectedDrones.Count} drones to move to {targetPosition}");
    }

    private List<Vector2> CalculateFormationPositions(Vector2 center, int droneCount)
    {
        var positions = new List<Vector2>();
        
        if (droneCount == 1)
        {
            positions.Add(center);
            return positions;
        }
        
        // Arrange in concentric circles
        int dronesPlaced = 0;
        int ring = 0;
        
        while (dronesPlaced < droneCount)
        {
            int dronesInRing = ring == 0 ? 1 : ring * 6; // Center + hexagonal rings
            dronesInRing = Mathf.Min(dronesInRing, droneCount - dronesPlaced);
            
            if (ring == 0)
            {
                positions.Add(center);
                dronesPlaced++;
            }
            else
            {
                float ringRadius = ring * _formationSpacing;
                for (int i = 0; i < dronesInRing; i++)
                {
                    float angle = (i * 2 * Mathf.Pi) / dronesInRing;
                    Vector2 offset = new Vector2(
                        Mathf.Cos(angle) * ringRadius,
                        Mathf.Sin(angle) * ringRadius
                    );
                    positions.Add(center + offset);
                    dronesPlaced++;
                }
            }
            
            ring++;
        }
        
        return positions;
    }

    private void AssignControlGroup(int groupNumber)
    {
        if (_selectedDrones.Count == 0) return;
        
        _controlGroups[groupNumber] = new List<Drone>(_selectedDrones);
        
        // Set control group on drones
        foreach (var drone in _selectedDrones)
        {
            drone.ControlGroup = groupNumber;
        }
        
        GD.Print($"Assigned {_selectedDrones.Count} drones to control group {groupNumber}");
    }

    private void SelectControlGroup(int groupNumber)
    {
        if (!_controlGroups.ContainsKey(groupNumber)) return;
        
        ClearSelection();
        
        // Select all living drones in the group
        var groupDrones = _controlGroups[groupNumber].Where(d => d != null && d.CurrentState != Drone.DroneState.Destroyed).ToList();
        
        foreach (var drone in groupDrones)
        {
            AddToSelection(drone);
        }
        
        GD.Print($"Selected control group {groupNumber}: {groupDrones.Count} drones");
    }

    public void ExecutePattern(Pattern pattern)
    {
        if (_selectedDrones.Count == 0)
        {
            GD.Print("No drones selected for pattern execution");
            return;
        }
        
        if (!pattern.CanExecute(_selectedDrones))
        {
            GD.Print($"Cannot execute pattern {pattern.Name}: Requirements not met");
            return;
        }
        
        _isExecutingPattern = true;
        _queuedPattern = pattern;
        
        // Execute pattern on selected drones
        foreach (var drone in _selectedDrones)
        {
            drone.ExecutePattern(pattern);
        }
        
        // Record pattern usage
        pattern.RecordUse(true); // We'll update this based on actual results later
        
        EmitSignal(SignalName.PatternExecuted, pattern, _selectedDrones);
        GD.Print($"Executing pattern {pattern.Name} with {_selectedDrones.Count} drones");
    }

    private void OnDroneDestroyed(Drone drone)
    {
        _allDrones.Remove(drone);
        _selectedDrones.Remove(drone);
        
        // Remove from control groups
        foreach (var group in _controlGroups.Values)
        {
            group.Remove(drone);
        }
        
        GD.Print($"Drone destroyed. Remaining: {_allDrones.Count}");
        
        // Check if all drones are destroyed
        if (_allDrones.Count == 0)
        {
            EmitSignal(SignalName.AllDronesDestroyed);
        }
    }

    private void OnDroneSelected(Drone drone)
    {
        // Handled by HandleDroneClicked
    }

    private void OnDroneDeselected(Drone drone)
    {
        // Handled by selection system
    }

    private void OnDronePatternComplete(Drone drone)
    {
        // Check if all drones have completed the pattern
        bool allComplete = true;
        foreach (var selectedDrone in _selectedDrones)
        {
            if (selectedDrone.CurrentState == Drone.DroneState.ExecutingPattern)
            {
                allComplete = false;
                break;
            }
        }
        
        if (allComplete && _isExecutingPattern)
        {
            _isExecutingPattern = false;
            GD.Print($"Pattern {_queuedPattern?.Name} execution completed");
        }
    }

    private void DrawSelectionBox()
    {
        if (!_isSelecting) return;
        
        Rect2 rect = new Rect2(_selectionStart, _selectionEnd - _selectionStart);
        rect = rect.Abs();
        
        // Draw selection rectangle
        _selectionBox.DrawRect(rect, Colors.Cyan, false, 2.0f);
        _selectionBox.DrawRect(rect, new Color(Colors.Cyan.R, Colors.Cyan.G, Colors.Cyan.B, 0.1f));
    }

    // Public getters
    public List<Drone> GetAllDrones() => new List<Drone>(_allDrones);
    public List<Drone> GetSelectedDrones() => new List<Drone>(_selectedDrones);
    public int GetDroneCount() => _allDrones.Count;
    public int GetSelectedCount() => _selectedDrones.Count;
    
    public List<Drone> GetDronesByType(Drone.DroneType type)
    {
        return _allDrones.Where(d => d.Type == type).ToList();
    }
    
    public Dictionary<Drone.DroneType, int> GetDroneTypeCounts()
    {
        var counts = new Dictionary<Drone.DroneType, int>();
        foreach (var drone in _allDrones)
        {
            if (counts.ContainsKey(drone.Type))
                counts[drone.Type]++;
            else
                counts[drone.Type] = 1;
        }
        return counts;
    }
}
