[gd_scene load_steps=6 format=3]

[ext_resource type="Script" path="res://scripts/entities/Beast.cs" id="1_beast"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 15.0

[sub_resource type="CircleShape2D" id="CircleShape2D_2"]
radius = 200.0

[sub_resource type="CircleShape2D" id="CircleShape2D_3"]
radius = 50.0

[sub_resource type="CircleShape2D" id="CircleShape2D_4"]
radius = 30.0

[node name="Beast" type="CharacterBody2D"]
collision_layer = 2
collision_mask = 1
script = ExtResource("1_beast")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0.5, 0.2, 0.8, 1)
scale = Vector2(30, 30)

[node name="NavigationAgent2D" type="NavigationAgent2D" parent="."]
path_desired_distance = 8.0
target_desired_distance = 8.0

[node name="DetectionArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="DetectionCollision" type="CollisionShape2D" parent="DetectionArea"]
shape = SubResource("CircleShape2D_2")

[node name="AttackArea" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 1

[node name="AttackCollision" type="CollisionShape2D" parent="AttackArea"]
shape = SubResource("CircleShape2D_3")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

[node name="BehaviorTimer" type="Timer" parent="."]
wait_time = 1.0
autostart = true

[node name="AttackTimer" type="Timer" parent="."]
wait_time = 2.0

[node name="HealthBar" type="ProgressBar" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -25.0
offset_top = -40.0
offset_right = 25.0
offset_bottom = -35.0
max_value = 100.0
value = 100.0
show_percentage = false

[node name="SuspicionIndicator" type="Node2D" parent="."]

[node name="SuspicionBar" type="ProgressBar" parent="SuspicionIndicator"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -50.0
offset_right = 20.0
offset_bottom = -45.0
max_value = 1.0
step = 0.01
show_percentage = false
