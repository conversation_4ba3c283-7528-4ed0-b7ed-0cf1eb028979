using Godot;
using System.Collections.Generic;

public static class DefaultPatterns
{
    public static List<Pattern> GetStartingPatterns()
    {
        var patterns = new List<Pattern>();
        
        // Basic Spiral Pattern
        var spiral = new Pattern("Basic Spiral", Pattern.PatternType.Lure)
        {
            Description = "A simple spiral pattern to attract small beasts",
            EnergyCost = 10.0f,
            MinimumDrones = 3,
            OptimalDrones = 8,
            EffectivenessDecay = 0.01f,
            BaseEffectiveness = 0.8f,
            CurrentEffectiveness = 0.8f
        };
        
        // Simple 4-point spiral
        spiral.MovementSequence.Add(new Vector2(0, -30));
        spiral.MovementSequence.Add(new Vector2(30, 0));
        spiral.MovementSequence.Add(new Vector2(0, 30));
        spiral.MovementSequence.Add(new Vector2(-30, 0));
        
        spiral.TimingSequence.Add(0.0f);
        spiral.TimingSequence.Add(0.5f);
        spiral.TimingSequence.Add(1.0f);
        spiral.TimingSequence.Add(1.5f);
        
        patterns.Add(spiral);
        
        // Scatter Pattern
        var scatter = new Pattern("Scatter Formation", Pattern.PatternType.Distraction)
        {
            Description = "Drones spread out to confuse and distract beasts",
            EnergyCost = 8.0f,
            MinimumDrones = 5,
            OptimalDrones = 15,
            EffectivenessDecay = 0.005f,
            BaseEffectiveness = 0.6f,
            CurrentEffectiveness = 0.6f
        };
        
        // Random scatter points
        scatter.MovementSequence.Add(new Vector2(-40, -20));
        scatter.MovementSequence.Add(new Vector2(40, -20));
        scatter.MovementSequence.Add(new Vector2(-20, 40));
        scatter.MovementSequence.Add(new Vector2(20, 40));
        scatter.MovementSequence.Add(new Vector2(0, 0));
        
        scatter.TimingSequence.Add(0.0f);
        scatter.TimingSequence.Add(0.2f);
        scatter.TimingSequence.Add(0.4f);
        scatter.TimingSequence.Add(0.6f);
        scatter.TimingSequence.Add(0.8f);
        
        patterns.Add(scatter);
        
        // Line Formation
        var line = new Pattern("Line Advance", Pattern.PatternType.Attack)
        {
            Description = "Drones form a line and advance together",
            EnergyCost = 15.0f,
            MinimumDrones = 6,
            OptimalDrones = 12,
            EffectivenessDecay = 0.02f,
            BaseEffectiveness = 0.9f,
            CurrentEffectiveness = 0.9f
        };
        
        line.MovementSequence.Add(new Vector2(0, -50));
        line.MovementSequence.Add(new Vector2(0, -25));
        line.MovementSequence.Add(new Vector2(0, 0));
        
        line.TimingSequence.Add(0.0f);
        line.TimingSequence.Add(1.0f);
        line.TimingSequence.Add(2.0f);
        
        patterns.Add(line);
        
        return patterns;
    }
    
    public static List<Pattern> GetAdvancedPatterns()
    {
        var patterns = new List<Pattern>();
        
        // Complex Spiral
        var complexSpiral = Pattern.CreateSpiralPattern();
        complexSpiral.Name = "Complex Spiral";
        complexSpiral.Description = "An advanced spiral pattern with multiple loops";
        complexSpiral.IsUnlocked = false;
        complexSpiral.UnlockCondition = "Successfully hunt 10 beasts";
        complexSpiral.UnlockCost = 5;
        patterns.Add(complexSpiral);
        
        // Pincer Movement
        var pincer = Pattern.CreatePincerPattern();
        pincer.IsUnlocked = false;
        pincer.UnlockCondition = "Reach sector depth 3";
        pincer.UnlockCost = 8;
        patterns.Add(pincer);
        
        // Quantum Pattern (requires quantum drones)
        var quantum = new Pattern("Quantum Entanglement", Pattern.PatternType.Utility)
        {
            Description = "Quantum drones phase in and out of reality",
            EnergyCost = 25.0f,
            MinimumDrones = 4,
            OptimalDrones = 8,
            EffectivenessDecay = 0.03f,
            BaseEffectiveness = 1.2f,
            CurrentEffectiveness = 1.2f,
            IsUnlocked = false,
            UnlockCondition = "Discover quantum technology",
            UnlockCost = 15
        };
        
        quantum.RequiredDroneTypes.Add(Drone.DroneType.Quantum);
        
        // Phase pattern
        quantum.MovementSequence.Add(new Vector2(0, 0));
        quantum.MovementSequence.Add(new Vector2(50, 0));
        quantum.MovementSequence.Add(new Vector2(0, 0));
        quantum.MovementSequence.Add(new Vector2(-50, 0));
        quantum.MovementSequence.Add(new Vector2(0, 0));
        
        quantum.TimingSequence.Add(0.0f);
        quantum.TimingSequence.Add(0.3f);
        quantum.TimingSequence.Add(0.6f);
        quantum.TimingSequence.Add(0.9f);
        quantum.TimingSequence.Add(1.2f);
        
        patterns.Add(quantum);
        
        return patterns;
    }
    
    public static Pattern GetPatternByName(string name)
    {
        var allPatterns = new List<Pattern>();
        allPatterns.AddRange(GetStartingPatterns());
        allPatterns.AddRange(GetAdvancedPatterns());
        
        foreach (var pattern in allPatterns)
        {
            if (pattern.Name == name)
            {
                return pattern.Clone();
            }
        }
        
        return null;
    }
    
    public static List<Pattern> GetUnlockedPatterns()
    {
        var unlockedPatterns = new List<Pattern>();
        
        // For now, just return starting patterns
        // In a full implementation, this would check save data
        unlockedPatterns.AddRange(GetStartingPatterns());
        
        return unlockedPatterns;
    }
}
