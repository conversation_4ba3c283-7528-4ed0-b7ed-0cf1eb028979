using Godot;
using System;
using System.Collections.Generic;

public partial class SectorGenerator : Node
{
    [Signal]
    public delegate void SectorGeneratedEventHandler(Sector sector);

    public enum HazardType
    {
        None,
        RadiationField,
        GravityWell,
        QuantumStorm,
        DarkNebula,
        DebrisField
    }

    public class Sector
    {
        public Vector2I Position { get; set; }
        public int Depth { get; set; }
        public Vector2I Size { get; set; } = new Vector2I(4, 4);
        public Dictionary<Vector2I, Cell> Cells { get; set; } = new();
        public List<HazardType> ActiveHazards { get; set; } = new();
        public float BeastDensity { get; set; } = 1.0f;
        public float EvolutionRate { get; set; } = 1.0f;
        public float ResourceRichness { get; set; } = 1.0f;
        public float Visibility { get; set; } = 1.0f;
        public float AnomalyChance { get; set; } = 0.1f;
    }

    public class Cell
    {
        public Vector2I Position { get; set; }
        public HazardType Hazard { get; set; } = HazardType.None;
        public List<string> BeastTypes { get; set; } = new();
        public int BeastCount { get; set; } = 0;
        public bool HasAnomaly { get; set; } = false;
        public string AnomalyType { get; set; } = "";
        public Dictionary<string, float> Resources { get; set; } = new();
    }

    private Sector _currentSector;
    private Random _random = new Random();

    // Beast types available for spawning
    private readonly string[] _beastTypes = {
        "Void Lurker",
        "Quantum Wraith",
        "Plasma Leech",
        "Gravity Maw",
        "Phase Spider",
        "Nebula Drifter",
        "Crystal Swarm",
        "Dark Matter Entity"
    };

    // Anomaly types
    private readonly string[] _anomalyTypes = {
        "Derelict Ship",
        "Ancient Beacon",
        "Quantum Rift",
        "Resource Cache",
        "Data Core",
        "Void Echo",
        "Temporal Distortion",
        "Energy Nexus"
    };

    public override void _Ready()
    {
        GD.Print("SectorGenerator initialized");
    }

    public Sector GenerateNewSector(Vector2I position, int depth)
    {
        _random = new Random(GetSectorSeed(position, depth));
        
        var sector = new Sector
        {
            Position = position,
            Depth = depth
        };

        // Generate sector-wide properties based on depth
        GenerateSectorProperties(sector);
        
        // Generate individual cells
        GenerateCells(sector);
        
        // Apply sector-wide hazards
        ApplySectorHazards(sector);
        
        _currentSector = sector;
        EmitSignal(SignalName.SectorGenerated, sector);
        
        GD.Print($"Generated sector at {position}, depth {depth}");
        LogSectorInfo(sector);
        
        return sector;
    }

    private int GetSectorSeed(Vector2I position, int depth)
    {
        // Create deterministic seed based on position and depth
        return position.X.GetHashCode() ^ (position.Y.GetHashCode() << 2) ^ (depth.GetHashCode() << 4);
    }

    private void GenerateSectorProperties(Sector sector)
    {
        float depthModifier = 1.0f + (sector.Depth * 0.1f);
        
        // Beast density increases with depth
        sector.BeastDensity = Mathf.Clamp(_random.NextSingle() * 2.0f * depthModifier, 0.5f, 5.0f);
        
        // Evolution rate increases with depth
        sector.EvolutionRate = Mathf.Clamp(0.5f + (_random.NextSingle() * 1.5f * depthModifier), 0.25f, 2.0f);
        
        // Resource richness varies randomly but tends to increase with depth
        sector.ResourceRichness = Mathf.Clamp(0.5f + (_random.NextSingle() * 1.0f * depthModifier), 0.5f, 1.5f);
        
        // Visibility decreases with depth
        sector.Visibility = Mathf.Clamp(1.5f - (sector.Depth * 0.1f) + (_random.NextSingle() * 0.5f), 0.3f, 1.0f);
        
        // Anomaly chance increases with depth
        sector.AnomalyChance = Mathf.Clamp(0.05f + (sector.Depth * 0.02f) + (_random.NextSingle() * 0.1f), 0.0f, 0.3f);
    }

    private void GenerateCells(Sector sector)
    {
        for (int x = 0; x < sector.Size.X; x++)
        {
            for (int y = 0; y < sector.Size.Y; y++)
            {
                var cellPos = new Vector2I(x, y);
                var cell = GenerateCell(cellPos, sector);
                sector.Cells[cellPos] = cell;
            }
        }
    }

    private Cell GenerateCell(Vector2I position, Sector sector)
    {
        var cell = new Cell
        {
            Position = position
        };

        // Generate hazard for this cell
        cell.Hazard = GenerateHazard();
        
        // Generate beasts
        GenerateBeastsForCell(cell, sector);
        
        // Check for anomaly
        if (_random.NextSingle() < sector.AnomalyChance)
        {
            cell.HasAnomaly = true;
            cell.AnomalyType = _anomalyTypes[_random.Next(_anomalyTypes.Length)];
        }
        
        // Generate resources
        GenerateResourcesForCell(cell, sector);
        
        return cell;
    }

    private HazardType GenerateHazard()
    {
        // 60% chance of no hazard, 40% chance of some hazard
        if (_random.NextSingle() < 0.6f)
            return HazardType.None;
        
        var hazardTypes = Enum.GetValues<HazardType>();
        // Skip None (index 0)
        return hazardTypes[_random.Next(1, hazardTypes.Length)];
    }

    private void GenerateBeastsForCell(Cell cell, Sector sector)
    {
        // Base beast count modified by sector density
        float baseBeastChance = 0.7f * sector.BeastDensity;
        
        if (_random.NextSingle() < baseBeastChance)
        {
            // Determine number of beasts (1-3 typically)
            cell.BeastCount = _random.Next(1, Mathf.RoundToInt(3 * sector.BeastDensity) + 1);
            
            // Select beast types
            var availableTypes = new List<string>(_beastTypes);
            int typeCount = Mathf.Min(_random.Next(1, 3), cell.BeastCount);
            
            for (int i = 0; i < typeCount; i++)
            {
                if (availableTypes.Count > 0)
                {
                    int index = _random.Next(availableTypes.Count);
                    cell.BeastTypes.Add(availableTypes[index]);
                    availableTypes.RemoveAt(index);
                }
            }
        }
    }

    private void GenerateResourcesForCell(Cell cell, Sector sector)
    {
        // Base resources from beasts
        if (cell.BeastCount > 0)
        {
            float biomassAmount = cell.BeastCount * 10.0f * sector.ResourceRichness;
            cell.Resources["Biomass"] = biomassAmount;
            
            // Chance for rare resources
            if (_random.NextSingle() < 0.1f * sector.ResourceRichness)
            {
                cell.Resources["QuantumCores"] = _random.Next(1, 4);
            }
        }
        
        // Anomaly resources
        if (cell.HasAnomaly)
        {
            switch (cell.AnomalyType)
            {
                case "Resource Cache":
                    cell.Resources["Fuel"] = _random.Next(10, 30);
                    cell.Resources["LifeSupport"] = _random.Next(5, 15);
                    break;
                case "Data Core":
                    cell.Resources["QuantumCores"] = _random.Next(2, 6);
                    break;
                case "Energy Nexus":
                    cell.Resources["Fuel"] = _random.Next(20, 50);
                    break;
            }
        }
    }

    private void ApplySectorHazards(Sector sector)
    {
        // Determine sector-wide hazards
        int hazardCount = _random.Next(0, 3); // 0-2 sector-wide hazards
        
        var availableHazards = new List<HazardType>(Enum.GetValues<HazardType>());
        availableHazards.Remove(HazardType.None);
        
        for (int i = 0; i < hazardCount && availableHazards.Count > 0; i++)
        {
            int index = _random.Next(availableHazards.Count);
            sector.ActiveHazards.Add(availableHazards[index]);
            availableHazards.RemoveAt(index);
        }
    }

    private void LogSectorInfo(Sector sector)
    {
        GD.Print($"Sector Properties:");
        GD.Print($"  Beast Density: {sector.BeastDensity:F2}");
        GD.Print($"  Evolution Rate: {sector.EvolutionRate:F2}");
        GD.Print($"  Resource Richness: {sector.ResourceRichness:F2}");
        GD.Print($"  Visibility: {sector.Visibility:F2}");
        GD.Print($"  Anomaly Chance: {sector.AnomalyChance:F2}");
        GD.Print($"  Active Hazards: {string.Join(", ", sector.ActiveHazards)}");
        
        int totalBeasts = 0;
        int totalAnomalies = 0;
        
        foreach (var cell in sector.Cells.Values)
        {
            totalBeasts += cell.BeastCount;
            if (cell.HasAnomaly) totalAnomalies++;
        }
        
        GD.Print($"  Total Beasts: {totalBeasts}");
        GD.Print($"  Total Anomalies: {totalAnomalies}");
    }

    // Public getters
    public Sector GetCurrentSector() => _currentSector;
    
    public Cell GetCell(Vector2I position)
    {
        return _currentSector?.Cells.GetValueOrDefault(position);
    }
    
    public List<Cell> GetCellsWithBeasts()
    {
        var result = new List<Cell>();
        if (_currentSector == null) return result;
        
        foreach (var cell in _currentSector.Cells.Values)
        {
            if (cell.BeastCount > 0)
            {
                result.Add(cell);
            }
        }
        
        return result;
    }
    
    public List<Cell> GetCellsWithAnomalies()
    {
        var result = new List<Cell>();
        if (_currentSector == null) return result;
        
        foreach (var cell in _currentSector.Cells.Values)
        {
            if (cell.HasAnomaly)
            {
                result.Add(cell);
            }
        }
        
        return result;
    }
    
    public Dictionary<HazardType, int> GetHazardCounts()
    {
        var counts = new Dictionary<HazardType, int>();
        if (_currentSector == null) return counts;
        
        foreach (var cell in _currentSector.Cells.Values)
        {
            if (cell.Hazard != HazardType.None)
            {
                counts[cell.Hazard] = counts.GetValueOrDefault(cell.Hazard, 0) + 1;
            }
        }
        
        return counts;
    }

    // Hazard effect descriptions for UI
    public string GetHazardDescription(HazardType hazard)
    {
        return hazard switch
        {
            HazardType.RadiationField => "Drones decay over time, but beasts are sluggish",
            HazardType.GravityWell => "Pulls drones off-course, but creates predictable beast paths",
            HazardType.QuantumStorm => "Randomizes all patterns, high risk/reward",
            HazardType.DarkNebula => "Limited vision, must hunt by sound/vibration",
            HazardType.DebrisField => "Provides cover but blocks targeting locks",
            _ => "No environmental effects"
        };
    }
}
