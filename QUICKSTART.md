# VOID STALKER - Quick Start Guide

## Getting the Game Running

1. **Open in Godot**: Load `project.godot` in Godot 4.2+
2. **Build Project**: Press F5 or click the play button
3. **Select Main Scene**: Choose `scenes/MainScene.tscn` when prompted
   - Note: If you get format errors, see TROUBLESHOOTING.md

## First Steps

### Understanding the Interface

**Resource Panel (Bottom Left)**:
- **Fuel**: Consumed when moving between sectors and hunting
- **Life Support**: Slowly drains over time - crew dies without it
- **Hull**: Damaged during failed hunts and hazards
- **Biomass**: Gained from successful hunts, converts to other resources

**Info Panel (Bottom Right)**:
- Current sector position and depth
- Drone count (current/maximum)
- Selected drone count
- Game state

**Pattern Panel (Top Right)**:
- Three starting patterns: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Trap, <PERSON>atter Distraction
- Hunt controls: Start Hunt, Abort Hunt
- Hunt status display

### Basic Gameplay Loop

1. **Select Drones**: Left-click and drag to select multiple drones
2. **Move Drones**: Right-click to move selected drones
3. **Execute Patterns**: Select drones, then click a pattern button
4. **Start Hunt**: Click "Start Hunt" to begin hunting beasts
5. **Survive**: Watch your resources and hunt successfully to gain biomass

### Controls Reference

**Mouse Controls**:
- **Left Click**: Select single drone
- **Left Click + Drag**: Select multiple drones in box
- **Ctrl + Left Click**: Add/remove drone from selection
- **Right Click**: Move selected drones to location

**Keyboard Controls**:
- **1-9**: Select control group
- **Ctrl + 1-9**: Assign selected drones to control group
- **ESC**: Pause game

**Debug Controls** (for testing):
- **F1**: Spawn additional drone
- **F2**: Add test resources (biomass + fuel)
- **F3**: Start hunt immediately
- **F4**: Execute spiral pattern with selected drones

## Testing the Core Systems

### 1. RTS Controls Test
- Select some drones by clicking and dragging
- Right-click somewhere to move them
- Try selecting individual drones with Ctrl+Click
- Assign them to control group 1 with Ctrl+1
- Select the group with just 1

### 2. Pattern System Test
- Select 5-10 drones
- Click "Spiral Lure" to see them execute the pattern
- Watch the effectiveness percentage change based on drone count
- Try different patterns with different numbers of drones

### 3. Resource System Test
- Watch the resource bars slowly drain over time
- Press F2 to add test resources
- Notice how biomass can be converted to other resources

### 4. Hunt System Test
- Click "Start Hunt" to begin hunting
- Beasts will spawn and start investigating your drones
- Use patterns to lure and confuse beasts
- Watch beast behavior change as their suspicion increases
- Complete the hunt to gain biomass

### 5. Evolution System Test
- Complete several hunts using the same pattern repeatedly
- Notice how beast resistance to that pattern increases
- Try different patterns to see effectiveness differences

## Understanding Beast Behavior

**Beast States**:
- **Idle**: Patrolling around home position
- **Investigating**: Moving toward last known drone position
- **Hunting**: Actively pursuing a specific drone
- **Attacking**: Close enough to damage drones
- **Fleeing**: Overwhelmed and trying to escape

**Suspicion System**:
- Beasts become more suspicious when they detect drones
- Pattern execution increases suspicion faster
- High suspicion leads to hunting behavior
- Suspicion decreases when beasts feel safe

## Tips for Success

1. **Manage Resources**: Always keep an eye on fuel and life support
2. **Vary Patterns**: Don't overuse the same pattern - beasts learn!
3. **Formation Matters**: Spread drones out to avoid concentrated beast attacks
4. **Timing is Key**: Execute patterns when beasts are investigating, not hunting
5. **Know When to Retreat**: Use the abort hunt button if things go badly

## Common Issues

**No Drones Spawning**: Check the console for errors, ensure Drone.tscn exists
**Patterns Not Working**: Make sure you have enough drones selected for the pattern
**Hunt Won't Start**: Ensure there are beasts in the current sector
**Performance Issues**: Reduce drone count if framerate drops

## Next Steps

Once you've tested the basic systems:
1. Experiment with creating custom patterns
2. Try surviving multiple hunt cycles
3. Explore the procedural sector generation
4. Test the beast evolution system over multiple runs

## Development Notes

This is an early implementation focusing on core mechanics:
- Visual effects are minimal (colored squares for now)
- Audio is not yet implemented
- Beast types are basic but functional
- UI is functional but not polished

The goal is to prove the core gameplay loop works before adding polish!
