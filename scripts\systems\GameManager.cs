using Godot;
using System;
using System.Collections.Generic;

public partial class GameManager : Node
{
    [Signal]
    public delegate void GameStateChangedEventHandler(GameState newState);
    
    [Signal]
    public delegate void HuntStartedEventHandler();
    
    [Signal]
    public delegate void HuntCompletedEventHandler(bool success, Dictionary<string, float> resourcesGained);
    
    [Signal]
    public delegate void GameOverEventHandler(string reason);

    public enum GameState
    {
        MainMenu,
        InSector,
        Hunting,
        MothershipManagement,
        GameOver
    }

    public GameState CurrentState { get; private set; } = GameState.MainMenu;
    public int CurrentSectorDepth { get; private set; } = 1;
    public Vector2I CurrentSectorPosition { get; private set; } = Vector2I.Zero;
    public float GameTime { get; private set; } = 0.0f;
    
    // Game difficulty scaling
    public float DifficultyMultiplier => 1.0f + (CurrentSectorDepth * 0.1f);
    
    private Timer _gameTimer;
    private bool _isPaused = false;

    public override void _Ready()
    {
        // Initialize game timer
        _gameTimer = new Timer();
        _gameTimer.WaitTime = 1.0f; // 1 second intervals
        _gameTimer.Timeout += OnGameTimerTimeout;
        AddChild(_gameTimer);
        _gameTimer.Start();
        
        // Connect to other systems
        ConnectToSystems();
        
        GD.Print("GameManager initialized");
    }

    public override void _Input(InputEvent @event)
    {
        if (@event.IsActionPressed("pause_game"))
        {
            TogglePause();
        }
    }

    private void ConnectToSystems()
    {
        // Connect to ResourceManager
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        if (resourceManager != null)
        {
            resourceManager.ResourceDepleted += OnResourceDepleted;
            resourceManager.CriticalResource += OnCriticalResource;
        }
        
        // Connect to DroneSwarmManager
        var droneManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        if (droneManager != null)
        {
            droneManager.AllDronesDestroyed += OnAllDronesDestroyed;
        }
    }

    public void ChangeState(GameState newState)
    {
        if (CurrentState == newState) return;
        
        var oldState = CurrentState;
        CurrentState = newState;
        
        GD.Print($"Game state changed: {oldState} -> {newState}");
        EmitSignal(SignalName.GameStateChanged, (int)newState);
        
        HandleStateTransition(oldState, newState);
    }

    private void HandleStateTransition(GameState from, GameState to)
    {
        switch (to)
        {
            case GameState.Hunting:
                StartHunt();
                break;
            case GameState.MothershipManagement:
                EnterMothershipMode();
                break;
            case GameState.GameOver:
                HandleGameOver();
                break;
        }
    }

    public void StartHunt()
    {
        GD.Print("Starting hunt...");
        EmitSignal(SignalName.HuntStarted);
        
        // Consume fuel for hunt attempt
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.ConsumeResource(ResourceManager.ResourceType.Fuel, 2.0f);
    }

    public void CompleteHunt(bool success, Dictionary<string, float> resourcesGained = null)
    {
        GD.Print($"Hunt completed. Success: {success}");
        
        if (resourcesGained == null)
            resourcesGained = new Dictionary<string, float>();
            
        EmitSignal(SignalName.HuntCompleted, success, resourcesGained);
        
        // Process hunt results
        if (success)
        {
            ProcessSuccessfulHunt(resourcesGained);
        }
        else
        {
            ProcessFailedHunt();
        }
        
        // Return to sector view
        ChangeState(GameState.InSector);
    }

    private void ProcessSuccessfulHunt(Dictionary<string, float> resources)
    {
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        if (resourceManager == null) return;
        
        foreach (var kvp in resources)
        {
            if (Enum.TryParse<ResourceManager.ResourceType>(kvp.Key, out var resourceType))
            {
                resourceManager.AddResource(resourceType, kvp.Value);
            }
        }
    }

    private void ProcessFailedHunt()
    {
        // Failed hunts teach beasts - handled by BeastEvolutionDB
        var evolutionDB = GetNode<BeastEvolutionDB>("/root/BeastEvolutionDB");
        evolutionDB?.RecordFailedHunt();
        
        // Potential hull damage from failed hunt
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.ConsumeResource(ResourceManager.ResourceType.Hull, 5.0f);
    }

    public void MoveSector(Vector2I direction)
    {
        CurrentSectorPosition += direction;
        
        // Consume fuel for sector movement
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.ConsumeResource(ResourceManager.ResourceType.Fuel, 1.0f);
        
        // Generate new sector
        var sectorGenerator = GetNode<SectorGenerator>("/root/SectorGenerator");
        sectorGenerator?.GenerateNewSector(CurrentSectorPosition, CurrentSectorDepth);
        
        GD.Print($"Moved to sector: {CurrentSectorPosition}");
    }

    public void AdvanceDepth()
    {
        CurrentSectorDepth++;
        GD.Print($"Advanced to depth: {CurrentSectorDepth}");
    }

    private void EnterMothershipMode()
    {
        // Pause resource drain while in mothership
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.SetResourceDrainPaused(true);
    }

    public void ExitMothershipMode()
    {
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.SetResourceDrainPaused(false);
        ChangeState(GameState.InSector);
    }

    private void OnGameTimerTimeout()
    {
        if (!_isPaused && CurrentState != GameState.GameOver)
        {
            GameTime += 1.0f;
            
            // Daily resource consumption
            if (Mathf.FloorToInt(GameTime) % 86400 == 0) // 24 hours in seconds
            {
                var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
                resourceManager?.ConsumeResource(ResourceManager.ResourceType.LifeSupport, 1.0f);
            }
        }
    }

    private void OnResourceDepleted(ResourceManager.ResourceType resourceType)
    {
        string reason = resourceType switch
        {
            ResourceManager.ResourceType.Fuel => "Stranded in the void - no fuel remaining",
            ResourceManager.ResourceType.LifeSupport => "Crew suffocated - life support failed",
            ResourceManager.ResourceType.Hull => "Ship destroyed - hull integrity compromised",
            _ => "Unknown system failure"
        };
        
        TriggerGameOver(reason);
    }

    private void OnCriticalResource(ResourceManager.ResourceType resourceType)
    {
        GD.Print($"WARNING: {resourceType} critically low!");
        // Could trigger UI warnings, emergency protocols, etc.
    }

    private void OnAllDronesDestroyed()
    {
        TriggerGameOver("All drones destroyed - unable to hunt");
    }

    public void TriggerGameOver(string reason)
    {
        GD.Print($"Game Over: {reason}");
        ChangeState(GameState.GameOver);
        EmitSignal(SignalName.GameOver, reason);
    }

    private void HandleGameOver()
    {
        _gameTimer.Stop();
        // Save meta-progression data
        SaveMetaProgress();
    }

    private void SaveMetaProgress()
    {
        // TODO: Implement save system for patterns, unlocks, etc.
        GD.Print("Saving meta-progression data...");
    }

    public void TogglePause()
    {
        _isPaused = !_isPaused;
        GetTree().Paused = _isPaused;
        GD.Print($"Game {(_isPaused ? "paused" : "unpaused")}");
    }

    public void RestartGame()
    {
        // Reset game state but preserve meta-progression
        CurrentSectorDepth = 1;
        CurrentSectorPosition = Vector2I.Zero;
        GameTime = 0.0f;
        _isPaused = false;
        GetTree().Paused = false;
        
        // Reset systems
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.ResetResources();
        
        ChangeState(GameState.InSector);
        _gameTimer.Start();
        
        GD.Print("Game restarted");
    }
}
