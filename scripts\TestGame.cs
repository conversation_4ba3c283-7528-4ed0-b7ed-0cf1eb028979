using Godot;
using System;

// Simple test script to verify game systems work
public partial class TestGame : Node
{
    public override void _Ready()
    {
        GD.Print("=== VOID STALKER TEST ===");
        
        // Test system initialization
        TestSystemInitialization();
        
        // Test pattern creation
        TestPatternSystem();
        
        // Test resource management
        TestResourceSystem();
        
        // Generate initial sector
        TestSectorGeneration();
        
        GD.Print("=== TEST COMPLETE ===");
    }

    private void TestSystemInitialization()
    {
        GD.Print("Testing system initialization...");
        
        var gameManager = GetNode<GameManager>("/root/GameManager");
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        var droneManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        var evolutionDB = GetNode<BeastEvolutionDB>("/root/BeastEvolutionDB");
        var sectorGenerator = GetNode<SectorGenerator>("/root/SectorGenerator");
        var huntManager = GetNode<HuntManager>("/root/HuntManager");
        
        GD.Print($"GameManager: {(gameManager != null ? "✓" : "✗")}");
        GD.Print($"ResourceManager: {(resourceManager != null ? "✓" : "✗")}");
        GD.Print($"DroneSwarmManager: {(droneManager != null ? "✓" : "✗")}");
        GD.Print($"BeastEvolutionDB: {(evolutionDB != null ? "✓" : "✗")}");
        GD.Print($"SectorGenerator: {(sectorGenerator != null ? "✓" : "✗")}");
        GD.Print($"HuntManager: {(huntManager != null ? "✓" : "✗")}");
    }

    private void TestPatternSystem()
    {
        GD.Print("Testing pattern system...");
        
        var spiral = Pattern.CreateSpiralPattern();
        var pincer = Pattern.CreatePincerPattern();
        var scatter = Pattern.CreateScatterPattern();
        
        GD.Print($"Spiral Pattern: {spiral.Name} - Valid: {spiral.IsValid()}");
        GD.Print($"Pincer Pattern: {pincer.Name} - Valid: {pincer.IsValid()}");
        GD.Print($"Scatter Pattern: {scatter.Name} - Valid: {scatter.IsValid()}");
        
        if (!spiral.IsValid())
            GD.Print($"Spiral errors: {spiral.GetValidationErrors()}");
        if (!pincer.IsValid())
            GD.Print($"Pincer errors: {pincer.GetValidationErrors()}");
        if (!scatter.IsValid())
            GD.Print($"Scatter errors: {scatter.GetValidationErrors()}");
    }

    private void TestResourceSystem()
    {
        GD.Print("Testing resource system...");
        
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        if (resourceManager == null) return;
        
        GD.Print($"Initial Fuel: {resourceManager.GetResource(ResourceManager.ResourceType.Fuel)}");
        GD.Print($"Initial Life Support: {resourceManager.GetResource(ResourceManager.ResourceType.LifeSupport)}");
        GD.Print($"Initial Hull: {resourceManager.GetResource(ResourceManager.ResourceType.Hull)}");
        GD.Print($"Initial Biomass: {resourceManager.GetResource(ResourceManager.ResourceType.Biomass)}");
        
        // Test biomass conversion
        resourceManager.AddResource(ResourceManager.ResourceType.Biomass, 50.0f);
        bool converted = resourceManager.ConvertBiomass(ResourceManager.ResourceType.Fuel, 10.0f);
        GD.Print($"Biomass conversion test: {(converted ? "✓" : "✗")}");
    }

    private void TestSectorGeneration()
    {
        GD.Print("Testing sector generation...");
        
        var sectorGenerator = GetNode<SectorGenerator>("/root/SectorGenerator");
        if (sectorGenerator == null) return;
        
        var sector = sectorGenerator.GenerateNewSector(Vector2I.Zero, 1);
        if (sector != null)
        {
            GD.Print($"Generated sector at {sector.Position} with {sector.Cells.Count} cells");
            
            int totalBeasts = 0;
            int totalAnomalies = 0;
            
            foreach (var cell in sector.Cells.Values)
            {
                totalBeasts += cell.BeastCount;
                if (cell.HasAnomaly) totalAnomalies++;
            }
            
            GD.Print($"Total beasts: {totalBeasts}");
            GD.Print($"Total anomalies: {totalAnomalies}");
        }
    }

    public override void _Input(InputEvent @event)
    {
        // Test hotkeys
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            switch (keyEvent.Keycode)
            {
                case Key.F1:
                    TestSpawnDrone();
                    break;
                case Key.F2:
                    TestAddResources();
                    break;
                case Key.F3:
                    TestStartHunt();
                    break;
                case Key.F4:
                    TestPatternExecution();
                    break;
            }
        }
    }

    private void TestSpawnDrone()
    {
        var droneManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        var drone = droneManager?.SpawnDrone(Drone.DroneType.Basic);
        GD.Print($"Test spawned drone: {(drone != null ? "✓" : "✗")}");
    }

    private void TestAddResources()
    {
        var resourceManager = GetNode<ResourceManager>("/root/ResourceManager");
        resourceManager?.AddResource(ResourceManager.ResourceType.Biomass, 25.0f);
        resourceManager?.AddResource(ResourceManager.ResourceType.Fuel, 10.0f);
        GD.Print("Test added resources");
    }

    private void TestStartHunt()
    {
        var gameManager = GetNode<GameManager>("/root/GameManager");
        gameManager?.ChangeState(GameManager.GameState.Hunting);
        GD.Print("Test started hunt");
    }

    private void TestPatternExecution()
    {
        var droneManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        if (droneManager == null) return;
        
        var drones = droneManager.GetAllDrones();
        if (drones.Count > 0)
        {
            // Select first few drones
            for (int i = 0; i < Math.Min(5, drones.Count); i++)
            {
                drones[i].SetSelected(true);
            }
            
            var pattern = Pattern.CreateSpiralPattern();
            droneManager.ExecutePattern(pattern);
            GD.Print("Test executed spiral pattern");
        }
        else
        {
            GD.Print("No drones available for pattern test");
        }
    }
}
