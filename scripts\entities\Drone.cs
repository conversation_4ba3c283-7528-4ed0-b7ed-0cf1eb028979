using Godot;
using System;
using System.Collections.Generic;

public partial class Drone : CharacterBody2D
{
    [Signal]
    public delegate void DroneSelectedEvent<PERSON>andler(Drone drone);
    
    [Signal]
    public delegate void DroneDeselected<PERSON><PERSON><PERSON>andler(Drone drone);
    
    [Signal]
    public delegate void DroneDestroyedEventHandler(Drone drone);
    
    [Signal]
    public delegate void PatternCompleteEventHandler(Drone drone);

    public enum DroneType
    {
        Basic,
        Quantum,
        Salvage,
        Decoy,
        Tether,
        Mimic,
        Void
    }

    public enum DroneState
    {
        Idle,
        Moving,
        ExecutingPattern,
        Attacking,
        Destroyed
    }

    [Export] public DroneType Type { get; set; } = DroneType.Basic;
    [Export] public float MaxSpeed { get; set; } = 200.0f;
    [Export] public float MaxHealth { get; set; } = 100.0f;
    [Export] public float EnergyDrainRate { get; set; } = 1.0f;
    [Export] public float PatternEfficiency { get; set; } = 1.0f;

    public DroneState CurrentState { get; private set; } = DroneState.Idle;
    public float CurrentHealth { get; private set; }
    public float CurrentEnergy { get; private set; } = 100.0f;
    public int ControlGroup { get; set; } = -1;
    public bool IsSelected { get; private set; } = false;
    
    // Navigation and movement
    private NavigationAgent2D _navigationAgent;
    private Vector2 _targetPosition;
    private Vector2 _formationOffset = Vector2.Zero;
    
    // Pattern execution
    private Pattern _currentPattern;
    private int _patternStep = 0;
    private float _patternTimer = 0.0f;
    
    // Visual components
    private Sprite2D _sprite;
    private AnimationPlayer _animationPlayer;
    private Area2D _selectionArea;
    private CollisionShape2D _selectionCollision;
    
    // Selection visual
    private Node2D _selectionIndicator;
    
    // Combat
    private Area2D _combatArea;
    private Timer _energyDrainTimer;

    public override void _Ready()
    {
        CurrentHealth = MaxHealth;
        SetupComponents();
        SetupNavigation();
        SetupTimers();
        
        GD.Print($"Drone {GetInstanceId()} initialized as {Type}");
    }

    private void SetupComponents()
    {
        // Main sprite
        _sprite = new Sprite2D();
        AddChild(_sprite);
        
        // Animation player
        _animationPlayer = new AnimationPlayer();
        AddChild(_animationPlayer);
        
        // Selection area (larger than sprite for easier clicking)
        _selectionArea = new Area2D();
        _selectionCollision = new CollisionShape2D();
        var selectionShape = new CircleShape2D();
        selectionShape.Radius = 20.0f;
        _selectionCollision.Shape = selectionShape;
        _selectionArea.AddChild(_selectionCollision);
        AddChild(_selectionArea);
        
        // Connect selection signals
        _selectionArea.InputEvent += OnSelectionAreaInputEvent;
        
        // Selection indicator (hidden by default)
        _selectionIndicator = new Node2D();
        AddChild(_selectionIndicator);
        _selectionIndicator.Visible = false;
        
        // Combat area
        _combatArea = new Area2D();
        var combatCollision = new CollisionShape2D();
        var combatShape = new CircleShape2D();
        combatShape.Radius = 30.0f;
        combatCollision.Shape = combatShape;
        _combatArea.AddChild(combatCollision);
        AddChild(_combatArea);
        
        // Set collision layers
        _selectionArea.CollisionLayer = 1; // Drones layer
        _combatArea.CollisionLayer = 1;
        _combatArea.CollisionMask = 2; // Can detect beasts
    }

    private void SetupNavigation()
    {
        _navigationAgent = new NavigationAgent2D();
        _navigationAgent.PathDesiredDistance = 4.0f;
        _navigationAgent.TargetDesiredDistance = 4.0f;
        AddChild(_navigationAgent);
    }

    private void SetupTimers()
    {
        _energyDrainTimer = new Timer();
        _energyDrainTimer.WaitTime = 1.0f;
        _energyDrainTimer.Timeout += OnEnergyDrainTimeout;
        AddChild(_energyDrainTimer);
        _energyDrainTimer.Start();
    }

    public override void _PhysicsProcess(double delta)
    {
        if (CurrentState == DroneState.Destroyed) return;
        
        HandleMovement(delta);
        HandlePatternExecution(delta);
        UpdateVisuals();
    }

    private void HandleMovement(double delta)
    {
        if (CurrentState == DroneState.Moving || CurrentState == DroneState.ExecutingPattern)
        {
            Vector2 nextPosition = _navigationAgent.GetNextPathPosition();
            Vector2 direction = (nextPosition - GlobalPosition).Normalized();
            
            Velocity = direction * MaxSpeed;
            MoveAndSlide();
            
            // Check if we've reached the target
            if (_navigationAgent.IsNavigationFinished())
            {
                if (CurrentState == DroneState.Moving)
                {
                    SetState(DroneState.Idle);
                }
            }
        }
    }

    private void HandlePatternExecution(double delta)
    {
        if (CurrentState != DroneState.ExecutingPattern || _currentPattern == null) return;
        
        _patternTimer += (float)delta;
        
        // Check if it's time for the next pattern step
        if (_patternStep < _currentPattern.TimingSequence.Count && 
            _patternTimer >= _currentPattern.TimingSequence[_patternStep])
        {
            ExecutePatternStep(_patternStep);
            _patternStep++;
            
            // Check if pattern is complete
            if (_patternStep >= _currentPattern.MovementSequence.Count)
            {
                CompletePattern();
            }
        }
    }

    private void ExecutePatternStep(int step)
    {
        if (step >= _currentPattern.MovementSequence.Count) return;
        
        Vector2 patternMove = _currentPattern.MovementSequence[step];
        Vector2 worldTarget = GlobalPosition + patternMove;
        
        MoveTo(worldTarget);
        
        // Consume energy for pattern execution
        ConsumeEnergy(_currentPattern.EnergyCost * 0.1f); // Spread cost across steps
    }

    private void CompletePattern()
    {
        _currentPattern = null;
        _patternStep = 0;
        _patternTimer = 0.0f;
        SetState(DroneState.Idle);
        
        EmitSignal(SignalName.PatternComplete, this);
        GD.Print($"Drone {GetInstanceId()} completed pattern");
    }

    public void MoveTo(Vector2 targetPosition)
    {
        _targetPosition = targetPosition + _formationOffset;
        _navigationAgent.TargetPosition = _targetPosition;
        
        if (CurrentState != DroneState.ExecutingPattern)
        {
            SetState(DroneState.Moving);
        }
    }

    public void SetFormationOffset(Vector2 offset)
    {
        _formationOffset = offset;
    }

    public void ExecutePattern(Pattern pattern)
    {
        if (pattern == null || CurrentState == DroneState.Destroyed) return;
        
        // Check if we have enough energy
        if (CurrentEnergy < pattern.EnergyCost)
        {
            GD.Print($"Drone {GetInstanceId()} insufficient energy for pattern");
            return;
        }
        
        _currentPattern = pattern;
        _patternStep = 0;
        _patternTimer = 0.0f;
        SetState(DroneState.ExecutingPattern);
        
        GD.Print($"Drone {GetInstanceId()} executing pattern: {pattern.Name}");
    }

    public void SetSelected(bool selected)
    {
        if (IsSelected == selected) return;
        
        IsSelected = selected;
        _selectionIndicator.Visible = selected;
        
        if (selected)
        {
            EmitSignal(SignalName.DroneSelected, this);
        }
        else
        {
            EmitSignal(SignalName.DroneDeselected, this);
        }
    }

    public void TakeDamage(float damage)
    {
        CurrentHealth = Mathf.Max(CurrentHealth - damage, 0.0f);
        
        if (CurrentHealth <= 0)
        {
            DestroyDrone();
        }
        else
        {
            // Visual damage feedback
            PlayDamageAnimation();
        }
    }

    public void ConsumeEnergy(float amount)
    {
        CurrentEnergy = Mathf.Max(CurrentEnergy - amount, 0.0f);
        
        if (CurrentEnergy <= 0)
        {
            // Drone becomes less effective when out of energy
            PatternEfficiency = 0.5f;
        }
    }

    public void RestoreEnergy(float amount)
    {
        CurrentEnergy = Mathf.Min(CurrentEnergy + amount, 100.0f);
        
        if (CurrentEnergy > 20.0f)
        {
            PatternEfficiency = 1.0f;
        }
    }

    private void DestroyDrone()
    {
        SetState(DroneState.Destroyed);
        EmitSignal(SignalName.DroneDestroyed, this);
        
        // Play destruction animation
        PlayDestructionAnimation();
        
        // Remove from scene after animation
        var timer = GetTree().CreateTimer(1.0f);
        timer.Timeout += () => QueueFree();
        
        GD.Print($"Drone {GetInstanceId()} destroyed");
    }

    private void SetState(DroneState newState)
    {
        if (CurrentState == newState) return;
        
        CurrentState = newState;
        UpdateVisuals();
    }

    private void UpdateVisuals()
    {
        // Update sprite based on state and type
        // This would load appropriate sprites based on drone type and state
        // For now, just update modulation for feedback
        
        if (CurrentState == DroneState.Destroyed)
        {
            Modulate = Colors.Red;
        }
        else if (CurrentEnergy < 20.0f)
        {
            Modulate = Colors.Yellow;
        }
        else if (IsSelected)
        {
            Modulate = Colors.Cyan;
        }
        else
        {
            Modulate = Colors.White;
        }
    }

    private void PlayDamageAnimation()
    {
        // Flash red briefly
        var tween = CreateTween();
        tween.TweenProperty(this, "modulate", Colors.Red, 0.1f);
        tween.TweenProperty(this, "modulate", Colors.White, 0.1f);
    }

    private void PlayDestructionAnimation()
    {
        // Explosion effect
        var tween = CreateTween();
        tween.Parallel().TweenProperty(this, "scale", Vector2.Zero, 0.5f);
        tween.Parallel().TweenProperty(this, "modulate:a", 0.0f, 0.5f);
    }

    private void OnSelectionAreaInputEvent(Node viewport, InputEvent @event, long shapeIdx)
    {
        if (@event is InputEventMouseButton mouseEvent && mouseEvent.Pressed)
        {
            if (mouseEvent.ButtonIndex == MouseButton.Left)
            {
                // Handle selection - this will be managed by DroneSwarmManager
                var swarmManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
                swarmManager?.HandleDroneClicked(this, Input.IsActionPressed("ui_accept")); // Ctrl for multi-select
            }
        }
    }

    private void OnEnergyDrainTimeout()
    {
        if (CurrentState != DroneState.Destroyed)
        {
            ConsumeEnergy(EnergyDrainRate);
        }
    }

    // Getters for external systems
    public Vector2 GetPosition() => GlobalPosition;
    public DroneType GetDroneType() => Type;
    public float GetHealthPercentage() => CurrentHealth / MaxHealth;
    public float GetEnergyPercentage() => CurrentEnergy / 100.0f;
}
