# VOID STALKER - Troubleshooting Guide

## Common Issues and Solutions

### Scene Format Issues

**Problem**: "Main.tscn is in a format newer than the format supported by version v4.4.1"

**Solution**: 
1. Use `scenes/MainScene.tscn` instead of `scenes/Main.tscn`
2. The project.godot has been updated to point to MainScene.tscn
3. If you still have issues, create a new scene in Godot editor:
   - Scene → New Scene
   - Add Node2D as root, rename to "Main"
   - Save as MainScene.tscn
   - Manually add the UI components from the script

### C# Compilation Issues

**Problem**: C# scripts not compiling or "MSBuild not found"

**Solutions**:
1. **Install .NET SDK**: Download and install .NET 6.0 or later SDK
2. **Restart Godot**: Close and reopen Godot after installing .NET
3. **Build Project**: Go to Project → Tools → C# → Create C# Solution
4. **Check Build Output**: Look at the bottom panel for build errors

**Problem**: "Assembly not found" errors

**Solutions**:
1. Go to Project → Project Settings → Dotnet → Project → Assembly Name
2. Ensure it matches "VoidStalker" as set in project.godot
3. Try Project → Tools → C# → Rebuild Project

### Runtime Errors

**Problem**: "Node not found" errors in console

**Solutions**:
1. Check that all autoload systems are properly configured in Project Settings
2. Verify scene paths in project.godot autoload section
3. Make sure all script files exist in the correct locations

**Problem**: Drones not spawning

**Solutions**:
1. Check that Drone.tscn exists in scenes/entities/
2. Verify DroneSwarmManager is loaded as autoload
3. Check console for specific error messages
4. Try pressing F1 to manually spawn a drone

**Problem**: UI not responding

**Solutions**:
1. Check that MainUI.cs is attached to the UI CanvasLayer
2. Verify all UI node paths in MainUI.cs GetUIReferences() method
3. Look for null reference exceptions in console

### Performance Issues

**Problem**: Low framerate with many drones

**Solutions**:
1. Reduce starting drone count in DroneSwarmManager
2. Disable debug output (comment out GD.Print statements)
3. Use Godot's profiler: Debug → Profiler

### Alternative Setup (If Scene Issues Persist)

If you continue having scene format issues, you can set up the project manually:

1. **Create New Project**: Start with a fresh Godot 4.4.1 project
2. **Copy Scripts**: Copy all files from the `scripts/` directory
3. **Set Up Autoloads**: 
   - Project → Project Settings → Autoload
   - Add each system script as an autoload:
     - GameManager: scripts/systems/GameManager.cs
     - ResourceManager: scripts/systems/ResourceManager.cs
     - DroneSwarmManager: scripts/systems/DroneSwarmManager.cs
     - BeastEvolutionDB: scripts/systems/BeastEvolutionDB.cs
     - SectorGenerator: scripts/systems/SectorGenerator.cs
     - HuntManager: scripts/systems/HuntManager.cs

4. **Create Scenes Manually**:
   - Create Drone scene: CharacterBody2D with Drone.cs script
   - Create Beast scene: CharacterBody2D with Beast.cs script  
   - Create Main scene: Node2D with UI setup

5. **Build and Test**: Use F5 to build and run

### Debug Commands

Use these in-game to test systems:
- **F1**: Spawn test drone
- **F2**: Add test resources
- **F3**: Start hunt
- **F4**: Execute test pattern

### Console Commands for Testing

Open Godot's console and try these if needed:
```gdscript
# Test resource system
get_node("/root/ResourceManager").AddResource(0, 50.0) # Add fuel

# Test drone spawning  
get_node("/root/DroneSwarmManager").SpawnDrone(0) # Spawn basic drone

# Test sector generation
get_node("/root/SectorGenerator").GenerateNewSector(Vector2i(0,0), 1)
```

### Getting Help

If you're still having issues:

1. **Check Console Output**: Look for specific error messages
2. **Verify File Structure**: Ensure all files are in correct locations
3. **Test Step by Step**: Use debug keys to test individual systems
4. **Start Simple**: Comment out complex systems and add them back gradually

### Known Limitations

- Visual effects are minimal (colored shapes)
- No audio system yet
- Beast AI is basic but functional
- UI is functional but not polished
- Some edge cases in pattern execution may cause issues

The core systems should work even with basic visuals. Focus on testing the gameplay mechanics first!
