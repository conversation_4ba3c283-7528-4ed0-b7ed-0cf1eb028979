# VOID STALKER
## Survival RTS - Hunt or Die

A unique hybrid of real-time strategy and survival mechanics where you command drone swarms to hunt void beasts in procedurally generated sectors. Every failed hunt teaches the beasts new resistances, forcing constant adaptation and innovation.

### Core Concept
- **Hunt or Die**: Your mothership constantly drains resources - only successful hunts provide biomass for survival
- **Evolving AI**: Beasts learn from your patterns and develop resistances over time
- **Pattern-Based Combat**: Success depends on creating and executing complex drone movement patterns
- **Permadeath with Meta-Progression**: Death resets your run but patterns and unlocks persist

### Current Implementation Status

#### ✅ Completed Systems
- **Core Architecture**: Game Manager, Resource Manager, Drone Swarm Manager
- **RTS Controls**: Unit selection, formation movement, control groups (1-9)
- **Pattern System**: Configurable drone movement patterns with effectiveness tracking
- **Resource Management**: Fuel, Life Support, Hull, Biomass with automatic drain
- **Beast Evolution Database**: Persistent learning system that tracks pattern effectiveness
- **Procedural Generation**: Sector generation with hazards, beasts, and anomalies
- **Basic UI**: Resource bars, drone counts, pattern buttons, sector information

#### 🚧 In Development
- Beast AI and combat system
- Visual effects and animations
- Audio system
- Advanced pattern editor
- Mothership management interface

#### 📋 Planned Features
- Multiple beast types with unique behaviors
- Environmental hazards affecting gameplay
- Crew management system
- Technology tree and upgrades
- Multiple victory conditions
- Steam Workshop integration for custom patterns

### Technical Stack
- **Engine**: Godot 4.2
- **Languages**: C# (primary), GDScript (UI)
- **Architecture**: Entity-Component System with autoloaded managers
- **Data**: SQLite for beast evolution, JSON for save data

### Controls
- **Left Click**: Select drones
- **Right Click**: Move selected drones
- **Ctrl + Click**: Multi-select drones
- **1-9**: Select control group
- **Ctrl + 1-9**: Assign control group
- **ESC**: Pause game

### Getting Started

1. **Prerequisites**:
   - Godot 4.2 or later
   - .NET 6.0 SDK

2. **Setup**:
   ```bash
   git clone [repository-url]
   cd voidstalker
   # Open project.godot in Godot Editor
   ```

3. **First Run**:
   - The game starts with 20 basic drones
   - Try the three starting patterns: Spiral Lure, Pincer Trap, Scatter Distraction
   - Watch your resources - they constantly drain!
   - Experiment with different drone formations

### Architecture Overview

```
Systems/
├── GameManager          # Core game state and flow control
├── ResourceManager      # Survival mechanics and resource tracking
├── DroneSwarmManager    # RTS controls and unit management
├── BeastEvolutionDB     # AI learning and pattern resistance
└── SectorGenerator      # Procedural world generation

Entities/
├── Drone               # Individual RTS units with pattern execution
├── Beast               # AI opponents (planned)
└── Mothership          # Player base (planned)

Data/
├── Pattern             # Configurable movement sequences
├── BeastTypes/         # Beast definitions (planned)
└── SectorTemplates/    # Procedural generation data
```

### Pattern System

Patterns are the core gameplay mechanic:

```csharp
var pattern = new Pattern("Spiral Lure", Pattern.PatternType.Lure)
{
    EnergyCost = 15.0f,
    MinimumDrones = 5,
    OptimalDrones = 15,
    EffectivenessDecay = 0.02f
};

// Define movement sequence
pattern.MovementSequence.Add(new Vector2(0, -50));
pattern.MovementSequence.Add(new Vector2(50, 0));
// ... more movements

// Define timing
pattern.TimingSequence.Add(0.0f);
pattern.TimingSequence.Add(0.5f);
// ... more timings
```

### Contributing

This is an early-stage project. Key areas for contribution:
- Beast AI implementation
- Visual effects and polish
- Audio design
- Pattern balancing
- UI/UX improvements

### Development Roadmap

**Phase 1 (Current)**: Core RTS mechanics and survival loop
**Phase 2**: Beast AI and combat system
**Phase 3**: Procedural content and meta-progression
**Phase 4**: Polish, audio, and advanced features
**Phase 5**: Steam release and Workshop support

### License

[License information to be added]

---

*"In the void, adaptation is survival. Every pattern tells a story. Every hunt writes history."*
