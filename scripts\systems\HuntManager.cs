using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class HuntManager : Node
{
    [Signal]
    public delegate void HuntStartedEvent<PERSON>andler(SectorGenerator.Cell targetCell);
    
    [Signal]
    public delegate void HuntCompletedEventHandler(bool success, Dictionary<string, float> resourcesGained);
    
    [Signal]
    public delegate void BeastSpawnedEvent<PERSON>andler(Beast beast);
    
    [Signal]
    public delegate void AllBeastsDefeatedEventHandler();

    [Export] public PackedScene BeastScene { get; set; }
    [Export] public Vector2 HuntAreaSize { get; set; } = new Vector2(800, 600);
    [Export] public float HuntTimeLimit { get; set; } = 120.0f; // 2 minutes per hunt

    private bool _isHuntActive = false;
    private SectorGenerator.Cell _currentHuntCell;
    private List<Beast> _activeBeastsInHunt = new List<Beast>();
    private Timer _huntTimer;
    private float _huntStartTime;
    
    // Hunt statistics
    private int _dronesLostThisHunt = 0;
    private int _beastsKilledThisHunt = 0;
    private Dictionary<string, float> _resourcesGainedThisHunt = new Dictionary<string, float>();
    
    // Pattern tracking for evolution system
    private List<string> _patternsUsedThisHunt = new List<string>();
    
    // System references
    private GameManager _gameManager;
    private DroneSwarmManager _droneSwarmManager;
    private BeastEvolutionDB _evolutionDB;
    private SectorGenerator _sectorGenerator;

    public override void _Ready()
    {
        SetupHuntTimer();
        ConnectToSystems();
        
        // Load beast scene if not set
        if (BeastScene == null)
        {
            BeastScene = GD.Load<PackedScene>("res://scenes/entities/Beast.tscn");
        }
        
        GD.Print("HuntManager initialized");
    }

    private void SetupHuntTimer()
    {
        _huntTimer = new Timer();
        _huntTimer.WaitTime = HuntTimeLimit;
        _huntTimer.Timeout += OnHuntTimeExpired;
        _huntTimer.OneShot = true;
        AddChild(_huntTimer);
    }

    private void ConnectToSystems()
    {
        _gameManager = GetNode<GameManager>("/root/GameManager");
        _droneSwarmManager = GetNode<DroneSwarmManager>("/root/DroneSwarmManager");
        _evolutionDB = GetNode<BeastEvolutionDB>("/root/BeastEvolutionDB");
        _sectorGenerator = GetNode<SectorGenerator>("/root/SectorGenerator");
        
        if (_gameManager != null)
        {
            _gameManager.HuntStarted += OnGameManagerHuntStarted;
        }
        
        if (_droneSwarmManager != null)
        {
            _droneSwarmManager.PatternExecuted += OnPatternExecuted;
        }
    }

    public void StartHunt(SectorGenerator.Cell targetCell)
    {
        if (_isHuntActive)
        {
            GD.Print("Hunt already in progress");
            return;
        }
        
        _isHuntActive = true;
        _currentHuntCell = targetCell;
        _huntStartTime = Time.GetUnixTimeFromSystem();
        
        // Reset hunt statistics
        _dronesLostThisHunt = 0;
        _beastsKilledThisHunt = 0;
        _resourcesGainedThisHunt.Clear();
        _patternsUsedThisHunt.Clear();
        
        // Spawn beasts for this hunt
        SpawnBeastsForHunt(targetCell);
        
        // Start hunt timer
        _huntTimer.Start();
        
        EmitSignal(SignalName.HuntStarted, targetCell);
        GD.Print($"Hunt started in cell {targetCell.Position} with {targetCell.BeastCount} beasts");
    }

    private void SpawnBeastsForHunt(SectorGenerator.Cell cell)
    {
        if (BeastScene == null)
        {
            GD.PrintErr("BeastScene not loaded");
            return;
        }
        
        var random = new Random();
        
        // Spawn beasts based on cell data
        for (int i = 0; i < cell.BeastCount; i++)
        {
            // Select beast type
            string beastTypeName = cell.BeastTypes.Count > 0 ? 
                cell.BeastTypes[random.Next(cell.BeastTypes.Count)] : 
                "VoidLurker";
            
            if (Enum.TryParse<Beast.BeastType>(beastTypeName.Replace(" ", ""), out var beastType))
            {
                SpawnBeast(beastType, GetRandomHuntPosition());
            }
        }
    }

    private void SpawnBeast(Beast.BeastType type, Vector2 position)
    {
        var beastInstance = BeastScene.Instantiate<Beast>();
        beastInstance.Type = type;
        beastInstance.GlobalPosition = position;
        
        // Scale beast stats based on sector depth
        var currentSector = _sectorGenerator.GetCurrentSector();
        if (currentSector != null)
        {
            float depthModifier = 1.0f + (currentSector.Depth * 0.2f);
            beastInstance.MaxHealth *= depthModifier;
            beastInstance.AttackDamage *= depthModifier;
            beastInstance.MaxSpeed *= (1.0f + currentSector.Depth * 0.1f);
        }
        
        // Connect beast signals
        beastInstance.BeastDestroyed += OnBeastDestroyed;
        beastInstance.BeastAttackedDrone += OnBeastAttackedDrone;
        
        // Add to scene and tracking
        GetTree().CurrentScene.AddChild(beastInstance);
        _activeBeastsInHunt.Add(beastInstance);
        
        EmitSignal(SignalName.BeastSpawned, beastInstance);
        GD.Print($"Spawned {type} beast at {position}");
    }

    private Vector2 GetRandomHuntPosition()
    {
        var random = new Random();
        return new Vector2(
            (float)(random.NextDouble() - 0.5) * HuntAreaSize.X,
            (float)(random.NextDouble() - 0.5) * HuntAreaSize.Y
        );
    }

    private void OnBeastDestroyed(Beast beast, Dictionary<string, float> loot)
    {
        _activeBeastsInHunt.Remove(beast);
        _beastsKilledThisHunt++;
        
        // Add loot to hunt resources
        foreach (var kvp in loot)
        {
            if (_resourcesGainedThisHunt.ContainsKey(kvp.Key))
                _resourcesGainedThisHunt[kvp.Key] += kvp.Value;
            else
                _resourcesGainedThisHunt[kvp.Key] = kvp.Value;
        }
        
        GD.Print($"Beast {beast.Type} destroyed. Remaining: {_activeBeastsInHunt.Count}");
        
        // Check if all beasts are defeated
        if (_activeBeastsInHunt.Count == 0)
        {
            EmitSignal(SignalName.AllBeastsDefeated);
            CompleteHunt(true);
        }
    }

    private void OnBeastAttackedDrone(Beast beast, Drone drone)
    {
        // Track drone losses
        if (drone.CurrentState == Drone.DroneState.Destroyed)
        {
            _dronesLostThisHunt++;
        }
    }

    private void OnPatternExecuted(Pattern pattern, List<Drone> drones)
    {
        if (_isHuntActive)
        {
            _patternsUsedThisHunt.Add(pattern.Name);
            GD.Print($"Pattern {pattern.Name} used in hunt with {drones.Count} drones");
        }
    }

    private void OnHuntTimeExpired()
    {
        GD.Print("Hunt time expired - hunt failed");
        CompleteHunt(false);
    }

    private void OnGameManagerHuntStarted()
    {
        // This is called when GameManager starts a hunt
        // We need to determine which cell to hunt in
        var currentSector = _sectorGenerator.GetCurrentSector();
        if (currentSector != null)
        {
            var cellsWithBeasts = _sectorGenerator.GetCellsWithBeasts();
            if (cellsWithBeasts.Count > 0)
            {
                // For now, pick the first cell with beasts
                // In a full implementation, this would be player choice
                StartHunt(cellsWithBeasts[0]);
            }
            else
            {
                GD.Print("No beasts found in current sector");
                CompleteHunt(false);
            }
        }
    }

    public void CompleteHunt(bool success)
    {
        if (!_isHuntActive) return;
        
        _isHuntActive = false;
        _huntTimer.Stop();
        
        // Clean up any remaining beasts
        foreach (var beast in _activeBeastsInHunt.ToList())
        {
            if (IsInstanceValid(beast))
            {
                beast.QueueFree();
            }
        }
        _activeBeastsInHunt.Clear();
        
        // Record pattern usage in evolution database
        RecordPatternsInEvolution(success);
        
        // Calculate final hunt results
        var huntResults = CalculateHuntResults(success);
        
        // Emit completion signal
        EmitSignal(SignalName.HuntCompleted, success, huntResults);
        
        // Notify game manager
        _gameManager?.CompleteHunt(success, huntResults);
        
        LogHuntResults(success, huntResults);
    }

    private void RecordPatternsInEvolution(bool huntSuccess)
    {
        if (_evolutionDB == null || _currentHuntCell == null) return;
        
        // Record each pattern used against each beast type in the cell
        foreach (var patternName in _patternsUsedThisHunt)
        {
            foreach (var beastTypeName in _currentHuntCell.BeastTypes)
            {
                _evolutionDB.RecordPatternUse(beastTypeName, patternName, huntSuccess);
            }
        }
    }

    private Dictionary<string, float> CalculateHuntResults(bool success)
    {
        var results = new Dictionary<string, float>(_resourcesGainedThisHunt);
        
        if (success)
        {
            // Bonus resources for successful hunt
            if (results.ContainsKey("Biomass"))
                results["Biomass"] *= 1.2f; // 20% bonus
            else
                results["Biomass"] = 10.0f; // Minimum biomass reward
            
            // Efficiency bonus based on time taken
            float huntDuration = (float)(Time.GetUnixTimeFromSystem() - _huntStartTime);
            float timeBonus = Mathf.Max(1.0f - (huntDuration / HuntTimeLimit), 0.1f);
            
            foreach (var key in results.Keys.ToList())
            {
                results[key] *= (1.0f + timeBonus * 0.5f);
            }
        }
        else
        {
            // Failed hunt - minimal or no resources
            results.Clear();
            results["Biomass"] = 2.0f; // Small consolation prize
        }
        
        return results;
    }

    private void LogHuntResults(bool success, Dictionary<string, float> results)
    {
        GD.Print($"=== HUNT COMPLETED ===");
        GD.Print($"Success: {success}");
        GD.Print($"Beasts Killed: {_beastsKilledThisHunt}");
        GD.Print($"Drones Lost: {_dronesLostThisHunt}");
        GD.Print($"Patterns Used: {string.Join(", ", _patternsUsedThisHunt)}");
        GD.Print($"Resources Gained:");
        
        foreach (var kvp in results)
        {
            GD.Print($"  {kvp.Key}: {kvp.Value:F1}");
        }
        
        GD.Print($"======================");
    }

    // Public methods for external control
    public void AbortHunt()
    {
        if (_isHuntActive)
        {
            GD.Print("Hunt aborted by player");
            CompleteHunt(false);
        }
    }

    public bool IsHuntActive() => _isHuntActive;
    public int GetActiveBeastCount() => _activeBeastsInHunt.Count;
    public float GetRemainingHuntTime() => _huntTimer.TimeLeft;
    public SectorGenerator.Cell GetCurrentHuntCell() => _currentHuntCell;
    
    public List<Beast> GetActiveBeastsInHunt() => new List<Beast>(_activeBeastsInHunt);
    
    public Dictionary<string, object> GetHuntStatistics()
    {
        return new Dictionary<string, object>
        {
            ["is_active"] = _isHuntActive,
            ["beasts_killed"] = _beastsKilledThisHunt,
            ["drones_lost"] = _dronesLostThisHunt,
            ["patterns_used"] = _patternsUsedThisHunt.Count,
            ["time_remaining"] = _huntTimer.TimeLeft,
            ["resources_gained"] = _resourcesGainedThisHunt.Count
        };
    }
}
